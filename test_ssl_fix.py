#!/usr/bin/env python3
"""
Test SSL Fix

Simple test to verify SSL certificate fix is working
"""

# Fix SSL certificates BEFORE any other imports
import os
import sys

print("🔧 Applying SSL certificate fix...")

# Set SSL certificate environment variables immediately
try:
    import certifi
    cert_path = certifi.where()
    os.environ['SSL_CERT_FILE'] = cert_path
    os.environ['REQUESTS_CA_BUNDLE'] = cert_path
    os.environ['CURL_CA_BUNDLE'] = cert_path
    print(f"✅ SSL certificates configured: {cert_path}")
except ImportError as e:
    print(f"❌ Failed to import certifi: {e}")
    sys.exit(1)

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Environment variables loaded from .env")
except ImportError:
    print("⚠️ python-dotenv not available, skipping .env loading")

print("\n🧪 Testing imports...")

try:
    print("Testing basic imports...")
    import asyncio
    import websockets
    import json
    print("✅ Basic imports successful")
except Exception as e:
    print(f"❌ Basic imports failed: {e}")
    sys.exit(1)

try:
    print("Testing utils import...")
    from utils import get_transcribe_audio, create_tts_response, index_exists
    print("✅ Utils import successful")
except Exception as e:
    print(f"❌ Utils import failed: {e}")
    sys.exit(1)

try:
    print("Testing config manager import...")
    from src.config_manager import VoiceAssistantConfig
    print("✅ Config manager import successful")
except Exception as e:
    print(f"❌ Config manager import failed: {e}")
    sys.exit(1)

try:
    print("Testing questionnaire handler import...")
    from questionnaire_handler import QuestionnaireHandler
    print("✅ Questionnaire handler import successful")
except Exception as e:
    print(f"❌ Questionnaire handler import failed: {e}")
    sys.exit(1)

try:
    print("Testing websocket voice server import...")
    from websocket_voice_server import start_voice_server
    print("✅ WebSocket voice server import successful")
except Exception as e:
    print(f"❌ WebSocket voice server import failed: {e}")
    sys.exit(1)

print("\n🎉 All imports successful!")
print("🚀 SSL certificate fix is working correctly")

# Test SSL connection
try:
    print("\n🌐 Testing SSL connection...")
    import requests
    response = requests.get("https://httpbin.org/get", timeout=10)
    if response.status_code == 200:
        print("✅ SSL connection test successful")
    else:
        print(f"⚠️ SSL test returned status code: {response.status_code}")
except Exception as e:
    print(f"❌ SSL connection test failed: {e}")

print("\n✨ Test completed successfully!")
print("You can now run the voice server with:")
print("python start_voice_server.py")
