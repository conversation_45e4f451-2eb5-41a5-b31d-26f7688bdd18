"""
Runtime hook for tiktoken to fix encoding issues in PyInstaller executables
"""

import os
import sys

# Fix tiktoken encoding issues when running as PyInstaller executable
if getattr(sys, 'frozen', False):
    # We're running in a PyInstaller bundle
    try:
        import tiktoken
        
        # Monkey patch tiktoken to handle the encoding issue
        original_get_encoding = tiktoken.get_encoding
        
        def patched_get_encoding(encoding_name):
            """Patched version that handles the character corruption issue"""
            # Fix common character corruption in encoding names
            if encoding_name == "c|100k_base":
                encoding_name = "cl100k_base"
            elif encoding_name == "r50k_base":
                encoding_name = "r50k_base"
            elif encoding_name == "p50k_base":
                encoding_name = "p50k_base"
            elif encoding_name == "o200k_base":
                encoding_name = "o200k_base"
            
            return original_get_encoding(encoding_name)
        
        # Apply the patch
        tiktoken.get_encoding = patched_get_encoding
        
        # Also patch encoding_for_model
        original_encoding_for_model = tiktoken.encoding_for_model
        
        def patched_encoding_for_model(model_name):
            """Patched version for model encoding"""
            try:
                return original_encoding_for_model(model_name)
            except Exception:
                # Fallback to cl100k_base for most modern models
                return tiktoken.get_encoding("cl100k_base")
        
        tiktoken.encoding_for_model = patched_encoding_for_model
        
    except ImportError:
        pass  # tiktoken not available
