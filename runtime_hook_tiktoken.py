"""
Runtime hook for tiktoken to fix encoding issues in PyInstaller executables
"""

import os
import sys

# Fix tiktoken encoding issues when running as PyInstaller executable
if getattr(sys, 'frozen', False):
    # We're running in a PyInstaller bundle
    try:
        import tiktoken
        import tiktoken.core
        import tiktoken.load

        # Store original functions
        original_get_encoding = tiktoken.get_encoding
        original_encoding_for_model = tiktoken.encoding_for_model
        original_load_tiktoken_bpe = tiktoken.load.load_tiktoken_bpe

        def patched_get_encoding(encoding_name):
            """Patched version that handles the character corruption issue"""
            # Fix common character corruption in encoding names
            encoding_fixes = {
                "c|100k_base": "cl100k_base",
                "c1100k_base": "cl100k_base",
                "cl|00k_base": "cl100k_base",
                "cl1|0k_base": "cl100k_base",
                "cl10|k_base": "cl100k_base",
                "cl100|_base": "cl100k_base",
                "cl100k|base": "cl100k_base",
                "r50k_base": "r50k_base",
                "p50k_base": "p50k_base",
                "o200k_base": "o200k_base",
                "gpt2": "gpt2"
            }

            # Apply fix if needed
            if encoding_name in encoding_fixes:
                encoding_name = encoding_fixes[encoding_name]

            try:
                return original_get_encoding(encoding_name)
            except Exception as e:
                print(f"Tiktoken encoding error for '{encoding_name}': {e}")
                # Fallback to cl100k_base for most cases
                return original_get_encoding("cl100k_base")

        def patched_encoding_for_model(model_name):
            """Patched version for model encoding"""
            try:
                return original_encoding_for_model(model_name)
            except Exception as e:
                print(f"Tiktoken model encoding error for '{model_name}': {e}")
                # Fallback to cl100k_base for most modern models
                return patched_get_encoding("cl100k_base")

        def patched_load_tiktoken_bpe(tiktoken_bpe_file, expected_hash=None):
            """Patched version of load_tiktoken_bpe that handles missing files"""
            try:
                return original_load_tiktoken_bpe(tiktoken_bpe_file, expected_hash)
            except Exception as e:
                print(f"Tiktoken BPE load error: {e}")
                # Try to load from bundled resources
                if hasattr(sys, '_MEIPASS'):
                    import pathlib
                    bundled_path = pathlib.Path(sys._MEIPASS) / "tiktoken" / tiktoken_bpe_file
                    if bundled_path.exists():
                        return original_load_tiktoken_bpe(str(bundled_path), expected_hash)
                raise e

        # Apply all patches
        tiktoken.get_encoding = patched_get_encoding
        tiktoken.encoding_for_model = patched_encoding_for_model
        tiktoken.load.load_tiktoken_bpe = patched_load_tiktoken_bpe

        # Also patch the core module if it exists
        if hasattr(tiktoken.core, 'Encoding'):
            original_encoding_init = tiktoken.core.Encoding.__init__

            def patched_encoding_init(self, name, *args, **kwargs):
                # Fix encoding name before initialization
                encoding_fixes = {
                    "c|100k_base": "cl100k_base",
                    "c1100k_base": "cl100k_base",
                    "cl|00k_base": "cl100k_base"
                }
                if name in encoding_fixes:
                    name = encoding_fixes[name]
                return original_encoding_init(self, name, *args, **kwargs)

            tiktoken.core.Encoding.__init__ = patched_encoding_init

        print("Tiktoken patches applied successfully for PyInstaller executable")

    except ImportError:
        print("Tiktoken not available, skipping patches")
    except Exception as e:
        print(f"Error applying tiktoken patches: {e}")
