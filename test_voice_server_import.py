#!/usr/bin/env python3
"""
Test voice server import and tiktoken functionality
"""

def test_tiktoken_import():
    """Test tiktoken import and basic functionality"""
    try:
        import tiktoken
        print("✓ tiktoken imported successfully")
        
        # Test basic encoding
        encoding = tiktoken.get_encoding("cl100k_base")
        print("✓ cl100k_base encoding loaded successfully")
        
        # Test encoding some text
        test_text = "Hello, world!"
        tokens = encoding.encode(test_text)
        decoded = encoding.decode(tokens)
        print(f"✓ Encoding test: '{test_text}' -> {len(tokens)} tokens -> '{decoded}'")
        
        return True
    except Exception as e:
        print(f"✗ tiktoken error: {e}")
        return False

def test_voice_server_import():
    """Test voice server import"""
    try:
        from websocket_voice_server import handle_voice_stream
        print("✓ websocket_voice_server imported successfully")
        return True
    except Exception as e:
        print(f"✗ websocket_voice_server import error: {e}")
        return False

def test_langchain_import():
    """Test langchain import"""
    try:
        from langchain_community.chat_models import ChatLiteLLM
        print("✓ langchain imported successfully")
        return True
    except Exception as e:
        print(f"✗ langchain import error: {e}")
        return False

def test_all_dependencies():
    """Test all critical dependencies"""
    print("Voice Server Dependencies Test")
    print("=" * 35)
    
    tests = [
        ("tiktoken", test_tiktoken_import),
        ("voice server", test_voice_server_import),
        ("langchain", test_langchain_import),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\nTesting: {test_name}")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"  ERROR: {test_name} - {e}")
    
    print("\n" + "=" * 35)
    print(f"Dependency Test Results: {passed}/{total} passed")
    
    return passed == total

if __name__ == "__main__":
    success = test_all_dependencies()
    
    if success:
        print("\n✅ All dependencies are working correctly!")
    else:
        print("\n❌ Some dependencies failed. This may explain why the voice server isn't starting.")
