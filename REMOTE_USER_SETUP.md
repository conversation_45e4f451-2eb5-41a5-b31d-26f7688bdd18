# 🌍 Remote User Setup Guide

## For Users Connecting from Different Locations

This guide is for users who want to connect to the voice streaming server from a different location (not on the same local network).

## 📋 What You Need

1. **Python 3.7+** installed on your machine
2. **The client script:** `voice_streaming_client.py`
3. **Server connection details** (provided by server host)
4. **Internet connection**

## 🔧 Quick Setup

### 1. Install Dependencies
```bash
pip install websockets certifi python-dotenv
```

### 2. Get Connection Details
Ask the server host for:
- **Server Host/IP:** (e.g., `123.456.789.012` or `abc123.ngrok.io`)
- **Server Port:** (e.g., `8767` or `12345`)

### 3. Run the Client
```bash
python voice_streaming_client.py --server-host SERVER_HOST --server-port SERVER_PORT
```

**Example:**
```bash
# For direct IP connection
python voice_streaming_client.py --server-host 123.456.789.012 --server-port 8767

# For ngrok connection
python voice_streaming_client.py --server-host 0.tcp.ngrok.io --server-port 12345
```

## 🎯 Usage Steps

1. **Run the command above** → Browser opens automatically
2. **Click "Connect to Server"** → Should show "Connected to voice server"
3. **Click "Start Streaming"** → Allow microphone access when prompted
4. **Start talking** → Get real-time AI responses

## 🔍 Troubleshooting

### "Connection refused" or "Cannot connect"
- ✅ Check server host/port are correct
- ✅ Confirm server is running
- ✅ Check your internet connection
- ✅ Try different port if using ngrok

### "Microphone access denied"
- ✅ Allow microphone permissions in browser
- ✅ Check browser security settings
- ✅ For remote connections, server may need HTTPS

### "WebSocket connection failed"
- ✅ Verify server is accessible
- ✅ Check firewall settings
- ✅ Try connecting from server's location first

## 🌐 Connection Types

### **Direct IP Connection**
```bash
python voice_streaming_client.py --server-host 123.456.789.012 --server-port 8767
```
- Requires port forwarding on server's router
- Most reliable for permanent setup

### **ngrok Connection**
```bash
python voice_streaming_client.py --server-host 0.tcp.ngrok.io --server-port 12345
```
- Easiest for testing
- URL changes each time ngrok restarts

### **Cloud Server Connection**
```bash
python voice_streaming_client.py --server-host your-server.com --server-port 8767
```
- Most reliable for production use
- Requires cloud server setup

## 🔒 Security Notes

- 🛡️ Only connect to trusted servers
- 🔐 Server host controls all AI processing and API keys
- 🎙️ Your voice data is processed in real-time (not stored permanently)
- 🌐 Use VPN for additional security if needed

## 📞 Getting Help

If you can't connect:
1. **Contact the server host** to verify server is running
2. **Check connection details** are correct
3. **Try from a different network** to test connectivity
4. **Use browser developer tools** (F12) to check for errors

## ✨ Success Indicators

You know it's working when:
- ✅ Browser opens to client interface
- ✅ "Connected to voice server" status appears
- ✅ Audio level bars respond to your voice
- ✅ You see real-time transcription and AI responses

---

🎙️ **Ready to start voice chatting!** 

Just run the client command with the correct server details! 🚀
