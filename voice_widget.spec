# -*- mode: python ; coding: utf-8 -*-
"""
PyInstaller spec file for Voice Streaming Widget

This file configures how the voice widget should be packaged into an executable.
"""

import os
import sys
from pathlib import Path

# Get the current directory
current_dir = Path.cwd()

# Define data files to include
datas = [
    # Voice streaming component
    ('voice_streaming_component.html', '.'),

    # Configuration files
    ('config', 'config'),

    # Python files needed by the widget
    ('utils.py', '.'),
    ('websocket_voice_server.py', '.'),
    ('start_voice_server.py', '.'),

    # Additional Python modules that might be imported
    ('requirements.txt', '.'),
]

# Add tiktoken binary and data files
try:
    import tiktoken
    import os
    import glob

    tiktoken_path = os.path.dirname(tiktoken.__file__)
    print(f"Tiktoken path: {tiktoken_path}")

    # Include tiktoken Python files only (avoid binary duplication issues)
    for file_path in glob.glob(os.path.join(tiktoken_path, '*.py')):
        if os.path.isfile(file_path):
            filename = os.path.basename(file_path)
            datas.append((file_path, f'tiktoken/{filename}'))
            print(f"Added tiktoken Python file: {file_path}")

    # Include tiktoken data directory if it exists
    tiktoken_data_path = os.path.join(tiktoken_path, '_tiktoken_data')
    if os.path.exists(tiktoken_data_path):
        datas.append((tiktoken_data_path, 'tiktoken/_tiktoken_data'))
        print(f"Added tiktoken data: {tiktoken_data_path}")

    # Include any .tiktoken files (encoding data)
    for root, dirs, files in os.walk(tiktoken_path):
        for file in files:
            if file.endswith('.tiktoken'):
                full_path = os.path.join(root, file)
                rel_path = os.path.relpath(full_path, tiktoken_path)
                datas.append((full_path, f'tiktoken/{rel_path}'))
                print(f"Added tiktoken encoding file: {full_path}")

except ImportError:
    print("Tiktoken not found, skipping tiktoken data inclusion")
except Exception as e:
    print(f"Error including tiktoken data: {e}")

# Add agent and src directories if they exist
if (current_dir / 'agent').exists():
    datas.append(('agent', 'agent'))
    
if (current_dir / 'src').exists():
    datas.append(('src', 'src'))

# Hidden imports - modules that PyInstaller might miss
hiddenimports = [
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'PyQt6.QtWidgets',
    'PyQt6.QtWebEngineWidgets',
    'PyQt6.QtWebEngineCore',
    'websockets',
    'langchain',
    'langchain_community',
    'langchain_openai',
    'langchain_litellm',
    'faiss',
    'tiktoken',
    'pypdf',
    'streamlit',
    'fastapi',
    'uvicorn',
    'pydantic',
    'certifi',
    'dotenv',
    # Audio processing dependencies
    'numpy',
    'scipy',
    'librosa',
    'soundfile',
    'pyaudio',
    'wave',
    'io',
    'base64',
    'tempfile',
    'asyncio',
    'json',
    'logging',
    # Voice server specific imports
    'websocket_voice_server',
    'utils',
    'agent.agents',
    'src.config_manager',
    'src.questionnaire_handler',
    'src.llm_agents',
    # Tiktoken specific imports
    'tiktoken.core',
    'tiktoken.load',
    'tiktoken.registry',
    'tiktoken._tiktoken',
]

# Binaries to exclude (reduce size) - removed numpy and scipy as they're needed
excludes = [
    'tkinter',
    'matplotlib',
    'pandas',
    'jupyter',
    'notebook',
    'IPython',
]

# Collect tiktoken binaries separately to avoid path issues
binaries = []
try:
    import tiktoken
    import glob
    tiktoken_path = os.path.dirname(tiktoken.__file__)

    # Add tiktoken binary files to binaries section
    for file_path in glob.glob(os.path.join(tiktoken_path, '*.pyd')):
        if os.path.isfile(file_path):
            filename = os.path.basename(file_path)
            binaries.append((file_path, '.'))
            print(f"Added tiktoken binary: {file_path}")

except Exception as e:
    print(f"Error collecting tiktoken binaries: {e}")

# Analysis configuration
a = Analysis(
    ['voice_widget.py'],
    pathex=[str(current_dir)],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=['runtime_hook_tiktoken.py'],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# Remove duplicate files
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# Create executable
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='VoiceStreamingWidget',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # No console window
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # Add icon file path here if you have one
    version_file=None,  # Add version info file here if you have one
)

# Optional: Create a directory distribution instead of single file
# Uncomment the following lines if you prefer a directory distribution:

# coll = COLLECT(
#     exe,
#     a.binaries,
#     a.zipfiles,
#     a.datas,
#     strip=False,
#     upx=True,
#     upx_exclude=[],
#     name='VoiceStreamingWidget'
# )
