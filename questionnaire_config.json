{"questionnaire": [{"id": "new_deposit", "question": "Hello! I'll help you enter your new receipt information. Are you adding this receipt to an existing bank deposit or creating a new one?", "type": "boolean", "required": true, "validation_patterns": ["(.*(?:new|creating|create).*)", "(.*(?:existing|add|adding).*)", "(.*(?:yes|no).*)", "(.*(?:true|false).*)"], "help_text": "Please specify if you want to create a new deposit or add to an existing one. Say 'new' or 'creating' for a new deposit, or 'existing' or 'adding' for an existing deposit."}, {"id": "reference_number", "question": "What is the reference number you want to assign to the new bank deposit?", "type": "text", "required": true, "validation_patterns": ["(?:is|number|ref|reference)?\\s*([A-Za-z0-9]{6,})\\b", "\\b([A-Za-z0-9]{6,})\\b", "\\b([A-Za-z0-9]+)\\b"], "help_text": "Please provide an alphanumeric reference number for this bank deposit."}, {"id": "bank_code", "question": "Which bank code is this cash being deposited to?", "type": "number", "required": true, "validation_patterns": ["\\b(\\d+)\\b", "\\b(zero|one|two|three|four|five|six|seven|eight|nine|ten|eleven|twelve|thirteen|fourteen|fifteen|sixteen|seventeen|eighteen|nineteen|twenty|thirty|forty|fifty|sixty|seventy|eighty|ninety|hundred)\\b"], "help_text": "Please provide the bank code as a number."}, {"id": "customer_number", "question": "What is the customer's Name ID number?", "type": "number", "required": true, "validation_patterns": ["\\b(\\d+)\\b", "\\b(zero|one|two|three|four|five|six|seven|eight|nine|ten|eleven|twelve|thirteen|fourteen|fifteen|sixteen|seventeen|eighteen|nineteen|twenty|thirty|forty|fifty|sixty|seventy|eighty|ninety|hundred)\\b"], "help_text": "Please provide the customer ID number."}, {"id": "employee_number", "question": "What is the employee number of the person who is receipting the cash?", "type": "number", "required": true, "validation_patterns": ["\\b(\\d+)\\b", "\\b(zero|one|two|three|four|five|six|seven|eight|nine|ten|eleven|twelve|thirteen|fourteen|fifteen|sixteen|seventeen|eighteen|nineteen|twenty|thirty|forty|fifty|sixty|seventy|eighty|ninety|hundred)\\b"], "help_text": "Please provide the employee number."}, {"id": "pay_code", "question": "Which of the following is the pay code of the cash received: BUSINESS CHECK, <PERSON><PERSON><PERSON> DRAFT, CASH, CASHIER'S CHECK, CREDIT CARD, ELECTRONIC TRANSFER, LOAD PROCEEDS CHECK, MONEY ORDER, <PERSON><PERSON><PERSON><PERSON><PERSON> CHECK, or TRAVELER'S CHECK?", "type": "choice", "choices": ["BUSINESS CHECK", "BANK DRAFT", "CASH", "CASHIER'S CHECK", "CREDIT CARD", "ELECTRONIC TRANSFER", "LOAD PROCEEDS CHECK", "MONEY ORDER", "PERSONAL CHECK", "TRAVELER'S CHECK"], "required": true, "validation_patterns": ["\\b(business\\s*check)\\b", "\\b(bank\\s*draft)\\b", "\\b(cash)\\b", "\\b(cashier'?s?\\s*check)\\b", "\\b(credit\\s*card)\\b", "\\b(electronic\\s*transfer)\\b", "\\b(load\\s*proceeds\\s*check)\\b", "\\b(money\\s*order)\\b", "\\b(personal\\s*check)\\b", "\\b(traveler'?s?\\s*check)\\b"], "help_text": "Please choose one of the listed pay code options."}, {"id": "amount", "question": "What is the amount of the cash received?", "type": "number", "required": true, "validation_patterns": ["\\$?([\\d,]+(?:\\.\\d{1,2})?)", "([\\d,]+(?:\\.\\d{1,2})?)\\s*dollars?", "received\\s+\\$?([\\d,]+(?:\\.\\d{1,2})?)", "got\\s+\\$?([\\d,]+(?:\\.\\d{1,2})?)", "([\\d,]+(?:\\.\\d{1,2})?)", "\\b((?:one|two|three|four|five|six|seven|eight|nine)\\s+thousand)\\b", "\\b((?:one|two|three|four|five|six|seven|eight|nine)\\s+hundred(?:\\s+(?:one|two|three|four|five|six|seven|eight|nine|ten|eleven|twelve|thirteen|fourteen|fifteen|sixteen|seventeen|eighteen|nineteen|twenty|thirty|forty|fifty|sixty|seventy|eighty|ninety))?)\\b", "\\b(zero|one|two|three|four|five|six|seven|eight|nine|ten|eleven|twelve|thirteen|fourteen|fifteen|sixteen|seventeen|eighteen|nineteen|twenty|thirty|forty|fifty|sixty|seventy|eighty|ninety|hundred|thousand)\\b"], "help_text": "Please provide the cash amount as a number, for example '150.50' or '150 dollars'."}, {"id": "account_number", "question": "What is the account number?", "type": "number", "required": true, "validation_patterns": ["\\b(\\d+)\\b", "\\b((?:one|two|three|four|five|six|seven|eight|nine)\\s+thousand)\\b", "\\b((?:one|two|three|four|five|six|seven|eight|nine)\\s+hundred(?:\\s+(?:one|two|three|four|five|six|seven|eight|nine|ten|eleven|twelve|thirteen|fourteen|fifteen|sixteen|seventeen|eighteen|nineteen|twenty|thirty|forty|fifty|sixty|seventy|eighty|ninety))?)\\b", "\\b(zero|one|two|three|four|five|six|seven|eight|nine|ten|eleven|twelve|thirteen|fourteen|fifteen|sixteen|seventeen|eighteen|nineteen|twenty|thirty|forty|fifty|sixty|seventy|eighty|ninety|hundred|thousand)\\b"], "help_text": "Please provide the account number."}, {"id": "amount_in_100_bills", "question": "How much is paid in $100 bills? Amount should be either 0 or multipliers of 100.", "type": "number", "required": true, "validation_patterns": ["\\b(0|[1-9]\\d*00)\\b", "\\b(zero)\\b", "\\b(\\d+)\\s+hundred\\b", "\\b(one|two|three|four|five|six|seven|eight|nine|ten)\\s+hundred\\b", "\\b(\\d+)\\b"], "help_text": "Please provide an amount that is either 0 or a multiple of 100, like 0, 100, 200, 300, etc."}], "settings": {"completion_message": "Perfect! I've collected all the required information for your receipt. Let me process this bank deposit now.", "confirmation_message": "🎉 **Success!** Your receipt has been successfully added to the system. Thank you for using the receipt entry system!", "max_deviations": 5, "llm_intervention_prompt": "The user has deviated from the new receipt questionnaire flow. Help them get back on track while being friendly and helpful. The current question expects a specific type of answer - guide them to provide the correct information."}}