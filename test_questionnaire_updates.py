#!/usr/bin/env python3
"""
Test script to verify the updated questionnaire system works correctly
without date questions and automatically uses today's date.
"""

import json
from datetime import datetime
from questionnaire_handler import Question<PERSON><PERSON><PERSON><PERSON>

def test_questionnaire_structure():
    """Test that the questionnaire configuration is properly updated"""
    print("Testing questionnaire configuration...")
    
    handler = QuestionnaireHandler()
    questions = handler.questions
    
    # Check that we have 9 questions instead of 11 (removed 2 date questions)
    expected_count = 9
    actual_count = len(questions)
    print(f"Expected {expected_count} questions, found {actual_count}")
    assert actual_count == expected_count, f"Expected {expected_count} questions, but found {actual_count}"
    
    # Check that date questions are not present
    question_ids = [q["id"] for q in questions]
    print(f"Question IDs: {question_ids}")
    
    assert "deposit_date" not in question_ids, "deposit_date question should be removed"
    assert "receipt_date" not in question_ids, "receipt_date question should be removed"
    
    # Check that all other expected questions are present
    expected_ids = [
        "new_deposit", "reference_number", "bank_code", "customer_number",
        "employee_number", "pay_code", "amount", "account_number", "amount_in_100_bills"
    ]
    
    for expected_id in expected_ids:
        assert expected_id in question_ids, f"Expected question '{expected_id}' not found"
    
    print("✅ Questionnaire structure test passed!")

def test_form_data_preparation():
    """Test that form data preparation automatically includes today's date"""
    print("\nTesting form data preparation...")
    
    handler = QuestionnaireHandler()
    
    # Mock collected answers (without date fields)
    test_answers = {
        "new_deposit": True,
        "reference_number": "12345678CA",
        "bank_code": "1",
        "customer_number": "100",
        "employee_number": "200",
        "pay_code": "CASH",
        "amount": "500",
        "account_number": "3000",
        "amount_in_100_bills": "400"
    }
    
    # Prepare form data
    form_data = handler.prepare_form_data(test_answers)
    
    # Check that today's date is automatically added
    today_date = datetime.today().strftime("%m/%d/%y")
    
    assert "deposit_date" in form_data, "deposit_date should be in form data"
    assert "receipt_date" in form_data, "receipt_date should be in form data"
    assert form_data["deposit_date"] == today_date, f"deposit_date should be today's date ({today_date})"
    assert form_data["receipt_date"] == today_date, f"receipt_date should be today's date ({today_date})"
    
    print(f"✅ Form data includes today's date: {today_date}")
    print(f"   deposit_date: {form_data['deposit_date']}")
    print(f"   receipt_date: {form_data['receipt_date']}")

def test_questionnaire_flow():
    """Test basic questionnaire flow without date questions"""
    print("\nTesting questionnaire flow...")
    
    handler = QuestionnaireHandler()
    
    # Get first question
    first_question = handler.get_first_question()
    print(f"First question: {first_question}")
    assert ("new deposit" in first_question.lower() or "receipt" in first_question.lower()), "First question should be about new deposit or receipt"
    
    # Simulate answering questions
    test_responses = [
        "new",  # new_deposit
        "12345678CA",  # reference_number
        "1",  # bank_code
        "100",  # customer_number
        "200",  # employee_number
        "cash",  # pay_code
        "500",  # amount
        "3000",  # account_number
        "400"  # amount_in_100_bills
    ]
    
    for i, response in enumerate(test_responses):
        print(f"\nQuestion {i+1}: Processing response '{response}'")
        result, needs_llm, context = handler.process_user_input(response)
        
        if needs_llm:
            print(f"   LLM intervention needed: {context.get('mode', 'unknown')}")
        else:
            print(f"   Response: {result}")
            
        # Check if questionnaire is complete
        if handler.is_questionnaire_complete():
            print("   Questionnaire completed!")
            break
    
    # Verify all answers were collected
    progress = handler.get_progress()
    print(f"\nFinal progress: {progress['current_question']}/{progress['total_questions']}")
    print(f"Collected answers: {list(progress['collected_answers'].keys())}")
    
    print("✅ Questionnaire flow test completed!")

def main():
    """Run all tests"""
    print("🧪 Testing Updated Questionnaire System")
    print("=" * 50)
    
    try:
        test_questionnaire_structure()
        test_form_data_preparation()
        test_questionnaire_flow()
        
        print("\n" + "=" * 50)
        print("🎉 All tests passed! The questionnaire system has been successfully updated.")
        print("✅ Date questions removed")
        print("✅ Today's date automatically included")
        print("✅ Questionnaire flow working correctly")
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
