#!/usr/bin/env python3
"""
Simple test to check if the voice server is responding
"""

import asyncio
import websockets
import json
import time

async def test_voice_server():
    """Test if the voice server is responding"""
    try:
        # Connect to the voice server
        uri = "ws://localhost:8765"
        print(f"Connecting to {uri}...")
        
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to voice server!")
            
            # Send a ping message
            ping_message = {"type": "ping"}
            await websocket.send(json.dumps(ping_message))
            print("📤 Sent ping message")
            
            # Wait for response
            response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
            response_data = json.loads(response)
            print(f"📥 Received response: {response_data}")
            
            if response_data.get("status") == "pong":
                print("✅ Voice server is responding correctly!")
                return True
            else:
                print("❌ Unexpected response from voice server")
                return False
                
    except asyncio.TimeoutError:
        print("❌ Timeout waiting for response from voice server")
        return False
    except ConnectionRefusedError:
        print("❌ Could not connect to voice server (connection refused)")
        return False
    except Exception as e:
        print(f"❌ Error testing voice server: {e}")
        return False

def main():
    """Main test function"""
    print("🎙️ Voice Server Test")
    print("=" * 30)
    
    # Wait a moment for server to start
    print("⏳ Waiting 3 seconds for server to start...")
    time.sleep(3)
    
    # Run the test
    result = asyncio.run(test_voice_server())
    
    if result:
        print("\n🎉 Voice server test PASSED!")
    else:
        print("\n❌ Voice server test FAILED!")
    
    return result

if __name__ == "__main__":
    main()
