<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; font-size: 16px; }
        #log { background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; height: 300px; overflow-y: auto; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Voice Server WebSocket Test</h1>
    
    <div id="status" class="status info">Ready to test</div>
    
    <button onclick="testConnection()">Test WebSocket Connection</button>
    <button onclick="testMicrophone()">Test Microphone Access</button>
    <button onclick="clearLog()">Clear Log</button>
    
    <h3>Test Log:</h3>
    <div id="log"></div>

    <script>
        let websocket = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('log');
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue';
            logDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function testConnection() {
            log('Testing WebSocket connection to ws://localhost:8765...');
            updateStatus('Testing connection...', 'info');
            
            try {
                websocket = new WebSocket('ws://localhost:8765');
                
                websocket.onopen = function(event) {
                    log('✓ WebSocket connected successfully!', 'success');
                    updateStatus('Connected to voice server', 'success');
                    
                    // Send test message
                    const testMsg = {
                        type: 'test',
                        message: 'Hello from browser test'
                    };
                    websocket.send(JSON.stringify(testMsg));
                    log('Sent test message: ' + JSON.stringify(testMsg));
                };
                
                websocket.onmessage = function(event) {
                    log('Received response: ' + event.data, 'success');
                };
                
                websocket.onclose = function(event) {
                    log('WebSocket connection closed: ' + event.code + ' ' + event.reason);
                    updateStatus('Connection closed', 'error');
                };
                
                websocket.onerror = function(error) {
                    log('WebSocket error: ' + error, 'error');
                    updateStatus('Connection error', 'error');
                };
                
            } catch (error) {
                log('Failed to create WebSocket: ' + error, 'error');
                updateStatus('Failed to connect', 'error');
            }
        }
        
        async function testMicrophone() {
            log('Testing microphone access...');
            updateStatus('Testing microphone...', 'info');
            
            try {
                // Check if getUserMedia is available
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    throw new Error('getUserMedia not supported in this browser');
                }
                
                log('Requesting microphone permission...');
                
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true
                    }
                });
                
                log('✓ Microphone access granted!', 'success');
                updateStatus('Microphone access granted', 'success');
                
                // Test audio context
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                log('✓ AudioContext created successfully');
                
                // Stop the stream
                stream.getTracks().forEach(track => track.stop());
                audioContext.close();
                
                log('✓ Microphone test completed successfully', 'success');
                
            } catch (error) {
                log('✗ Microphone test failed: ' + error.message, 'error');
                updateStatus('Microphone access failed', 'error');
                
                if (error.name === 'NotAllowedError') {
                    log('Permission denied - user needs to allow microphone access', 'error');
                } else if (error.name === 'NotFoundError') {
                    log('No microphone found on this device', 'error');
                } else if (error.name === 'NotSupportedError') {
                    log('Microphone not supported in this browser/context', 'error');
                }
            }
        }
        
        // Auto-test connection on page load
        window.onload = function() {
            log('Page loaded - ready for testing');
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
