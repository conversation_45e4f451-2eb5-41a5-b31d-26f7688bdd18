#!/usr/bin/env python3
"""
Test the widget UI and voice streaming functionality
"""

import time
import asyncio
import websockets
import json
import psutil

def check_widget_running():
    """Check if the widget is running"""
    for proc in psutil.process_iter(['pid', 'name']):
        if proc.info['name'] == 'VoiceStreamingWidget.exe':
            print(f"✓ Widget running (PID: {proc.info['pid']})")
            return True
    print("✗ Widget not running")
    return False

async def test_websocket_detailed():
    """Test WebSocket connection with detailed logging"""
    try:
        print("Connecting to WebSocket server...")
        
        uri = "ws://localhost:8765"
        async with websockets.connect(uri, timeout=10) as websocket:
            print("✓ WebSocket connected successfully")
            
            # Test different message types
            test_messages = [
                {"type": "test", "message": "Hello"},
                {"type": "start_recording"},
                {"type": "audio_chunk", "audio_data": "dGVzdA=="},  # base64 "test"
                {"type": "stop_recording"}
            ]
            
            for msg in test_messages:
                print(f"Sending: {msg['type']}")
                await websocket.send(json.dumps(msg))
                
                # Try to receive response
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    print(f"  Response: {response}")
                except asyncio.TimeoutError:
                    print(f"  No response (normal for {msg['type']})")
            
            return True
            
    except Exception as e:
        print(f"✗ WebSocket error: {e}")
        return False

def main():
    """Main test function"""
    print("Voice Widget UI Test")
    print("=" * 30)
    
    # Check if widget is running
    if not check_widget_running():
        print("\nPlease start the Voice Widget first:")
        print(".\dist\VoiceStreamingWidget_Portable\VoiceStreamingWidget.exe")
        return
    
    # Wait a moment for server to be ready
    print("\nWaiting for voice server to start...")
    time.sleep(3)
    
    # Test WebSocket connection
    print("\nTesting WebSocket connection...")
    success = asyncio.run(test_websocket_detailed())
    
    if success:
        print("\n✅ WebSocket server is working!")
        print("\nNext steps:")
        print("1. Look at the widget window")
        print("2. Check if 'Start Streaming' button is enabled")
        print("3. Click 'Start Streaming' and check browser console for errors")
        print("4. If button is disabled, check the status message")
    else:
        print("\n❌ WebSocket server not responding")
        print("\nTroubleshooting:")
        print("1. Make sure the widget shows 'Server Status: Running'")
        print("2. Try clicking 'Start Server' in the widget")
        print("3. Check if port 8765 is blocked by firewall")

if __name__ == "__main__":
    main()
