#!/usr/bin/env python3
"""
Build Script for Voice Streaming Widget

This script handles the complete build process for creating the executable widget.
"""

import os
import sys
import shutil
import subprocess
import platform
from pathlib import Path

def check_requirements():
    """Check if all required packages are installed"""
    required_packages = {
        'PyQt6': 'PyQt6',
        'PyQt6-WebEngine': 'PyQt6.QtWebEngineWidgets',
        'pyinstaller': 'PyInstaller',
        'websockets': 'websockets',
        'langchain': 'langchain',
    }

    missing_packages = []

    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)

    if missing_packages:
        print("ERROR: Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\nInstall missing packages with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False

    print("SUCCESS: All required packages are installed")
    return True

def clean_build_dirs():
    """Clean previous build directories"""
    dirs_to_clean = ['build', 'dist', '__pycache__']

    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            print(f"Cleaning {dir_name}/")
            shutil.rmtree(dir_name)

def check_required_files():
    """Check if all required files exist"""
    required_files = [
        'voice_widget.py',
        'voice_streaming_component.html',
        'websocket_voice_server.py',
        'utils.py',
    ]
    
    missing_files = []
    
    for file_name in required_files:
        if not Path(file_name).exists():
            missing_files.append(file_name)
    
    if missing_files:
        print("ERROR: Missing required files:")
        for file_name in missing_files:
            print(f"   - {file_name}")
        return False

    print("SUCCESS: All required files found")
    return True

def build_executable():
    """Build the executable using PyInstaller"""
    print("Building executable...")

    # Use the spec file for consistent builds
    cmd = [sys.executable, '-m', 'PyInstaller', 'voice_widget.spec', '--clean']

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("SUCCESS: Build completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print("ERROR: Build failed!")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False

def create_portable_package():
    """Create a portable package with the executable and required files"""
    dist_dir = Path('dist')
    package_dir = dist_dir / 'VoiceStreamingWidget_Portable'
    
    if package_dir.exists():
        shutil.rmtree(package_dir)
    
    package_dir.mkdir(parents=True, exist_ok=True)
    
    # Copy executable
    exe_name = 'VoiceStreamingWidget.exe' if platform.system() == 'Windows' else 'VoiceStreamingWidget'
    exe_path = dist_dir / exe_name
    
    if exe_path.exists():
        shutil.copy2(exe_path, package_dir / exe_name)
        print(f"SUCCESS: Copied executable to {package_dir}")
    else:
        print(f"ERROR: Executable not found: {exe_path}")
        return False
    
    # Copy additional files
    additional_files = [
        'README.md',
        'STREAMING_VOICE_README.md',
    ]
    
    for file_name in additional_files:
        if Path(file_name).exists():
            shutil.copy2(file_name, package_dir)
    
    # Create a simple README for the portable package
    readme_content = """# Voice Streaming Widget - Portable

## 🚀 Quick Start

1. **Run the executable**: Double-click `VoiceStreamingWidget.exe` (Windows) or `VoiceStreamingWidget` (Linux/Mac)
2. **Click "Start Server"** to begin the voice service
3. **Click "Start Streaming"** in the widget to begin voice chat
4. **Speak naturally** and get AI responses

## ✨ Features

- **Standalone executable** - No installation required
- **System tray integration** - Minimize to system tray
- **Always-on-top option** - Keep widget visible
- **Hotkey support** - Press Ctrl+Shift+V to toggle visibility
- **Auto-start server** - Automatically starts voice server
- **Settings panel** - Customize widget behavior

## ⚙️ Settings

Right-click the system tray icon or click the settings button (⚙️) to access:
- Widget opacity
- Always-on-top behavior
- Auto-start options
- Server port configuration

## 🔧 Troubleshooting

- **Server won't start**: Check if port 8765 is available
- **No audio**: Ensure microphone permissions are granted
- **Widget not visible**: Press Ctrl+Shift+V or check system tray

## 📞 Support

For issues or questions, please refer to the main project documentation.
"""
    
    with open(package_dir / 'README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"SUCCESS: Portable package created: {package_dir}")
    return True

def main():
    """Main build process"""
    print("Voice Streaming Widget Build Script")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Check required files
    if not check_required_files():
        sys.exit(1)
    
    # Clean previous builds
    clean_build_dirs()
    
    # Build executable
    if not build_executable():
        sys.exit(1)
    
    # Create portable package
    if not create_portable_package():
        sys.exit(1)
    
    print("\nBuild completed successfully!")
    print("Check the 'dist/VoiceStreamingWidget_Portable' directory for your executable")

    # Show file sizes
    dist_dir = Path('dist')
    if dist_dir.exists():
        print("\nBuild Results:")
        for item in dist_dir.iterdir():
            if item.is_file():
                size_mb = item.stat().st_size / (1024 * 1024)
                print(f"   {item.name}: {size_mb:.1f} MB")
            elif item.is_dir():
                total_size = sum(f.stat().st_size for f in item.rglob('*') if f.is_file())
                size_mb = total_size / (1024 * 1024)
                print(f"   {item.name}/: {size_mb:.1f} MB")

if __name__ == "__main__":
    main()
