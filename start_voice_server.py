#!/usr/bin/env python3
"""
Voice Server Startup Script

Starts only the WebSocket voice server for remote client connections.
This is useful when you want to run the server on one machine and
connect clients from other machines.
"""

# Fix SSL certificates BEFORE any other imports
import os
import sys

# Set SSL certificate environment variables immediately
try:
    import certifi
    cert_path = certifi.where()
    os.environ['SSL_CERT_FILE'] = cert_path
    os.environ['REQUESTS_CA_BUNDLE'] = cert_path
    os.environ['CURL_CA_BUNDLE'] = cert_path
except ImportError:
    pass

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# Now safe to import other modules
import argparse
import logging
from websocket_voice_server import start_voice_server

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Main function to start the voice server"""

    parser = argparse.ArgumentParser(description="Voice Streaming Server")
    parser.add_argument("--host", default="localhost",
                       help="Server host (default: localhost, use 0.0.0.0 for all interfaces)")
    parser.add_argument("--port", type=int, default=8765,
                       help="Server port (default: 8765)")
    parser.add_argument("--debug", action="store_true",
                       help="Enable debug logging")

    args = parser.parse_args()
    
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        os.environ["LOG_LEVEL"] = "DEBUG"
        print("🐛 Debug mode enabled")

    print("🎙️ Starting Voice Streaming Server...")
    print("=" * 50)
    print(f"Host: {args.host}")
    print(f"Port: {args.port}")
    print(f"Debug: {args.debug}")
    print("=" * 50)

    # Flush output to ensure it's visible
    sys.stdout.flush()
    
    if args.host == "0.0.0.0":
        print("⚠️  Server will accept connections from any IP address")
        print("🔒 Make sure your firewall is properly configured")
    
    print("🚀 Server starting...")
    print("🛑 Press Ctrl+C to stop the server")
    print()
    sys.stdout.flush()

    try:
        print(f"📡 Calling start_voice_server(host='{args.host}', port={args.port})")
        sys.stdout.flush()
        start_voice_server(host=args.host, port=args.port)
    except KeyboardInterrupt:
        print("\n🛑 Shutting down voice server...")
        print("👋 Goodbye!")
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
