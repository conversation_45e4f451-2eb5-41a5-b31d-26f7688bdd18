#!/bin/bash

# GitHub Actions Setup Script for Voice Assistant API
# This script helps you set up GitHub Actions deployment for your repository

set -e

echo "🚀 GitHub Actions Setup for Voice Assistant API"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    print_error "This is not a git repository. Please run this script from your project root."
    exit 1
fi

# Check if GitHub CLI is installed
if ! command -v gh &> /dev/null; then
    print_warning "GitHub CLI (gh) is not installed. Some features will be limited."
    print_info "Install it from: https://cli.github.com/"
    USE_GH_CLI=false
else
    USE_GH_CLI=true
    print_success "GitHub CLI found"
fi

# Get repository information
REPO_URL=$(git config --get remote.origin.url)
if [[ $REPO_URL == *"github.com"* ]]; then
    REPO_NAME=$(basename "$REPO_URL" .git)
    REPO_OWNER=$(basename "$(dirname "$REPO_URL")" | sed 's/.*://')
    print_success "Repository: $REPO_OWNER/$REPO_NAME"
else
    print_error "This doesn't appear to be a GitHub repository"
    exit 1
fi

echo ""
echo "🔧 Setting up GitHub Actions workflows..."

# Check if workflows already exist
if [ -d ".github/workflows" ]; then
    print_warning "GitHub Actions workflows already exist"
    echo "Files found:"
    ls -la .github/workflows/
    echo ""
    read -p "Do you want to continue and potentially overwrite existing files? (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Setup cancelled"
        exit 0
    fi
else
    print_success "Creating .github/workflows directory"
fi

echo ""
echo "🔑 Repository Secrets Setup"
echo "=========================="

# Generate a secure API key if needed
if [ ! -f ".env" ] || ! grep -q "VOICE_API_KEY" .env; then
    print_info "Generating secure API key..."
    API_KEY=$(openssl rand -hex 32)
    echo "VOICE_API_KEY=$API_KEY" >> .env
    print_success "API key generated and saved to .env"
else
    API_KEY=$(grep "VOICE_API_KEY" .env | cut -d'=' -f2)
    print_success "Using existing API key from .env"
fi

echo ""
print_info "Required secrets for GitHub repository:"
echo "VOICE_API_KEY=$API_KEY"

if [ "$USE_GH_CLI" = true ]; then
    echo ""
    read -p "Do you want to set the VOICE_API_KEY secret now? (y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if gh secret set VOICE_API_KEY --body "$API_KEY"; then
            print_success "VOICE_API_KEY secret set successfully"
        else
            print_error "Failed to set secret. You can set it manually in GitHub repository settings."
        fi
    fi
else
    print_warning "Set this secret manually at: https://github.com/$REPO_OWNER/$REPO_NAME/settings/secrets/actions"
fi

echo ""
echo "🌍 Environment Setup"
echo "==================="

if [ "$USE_GH_CLI" = true ]; then
    echo ""
    read -p "Do you want to create staging and production environments? (y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # Create staging environment
        if gh api repos/$REPO_OWNER/$REPO_NAME/environments/staging -X PUT --input - <<< '{"wait_timer":0,"reviewers":[],"deployment_branch_policy":{"protected_branches":false,"custom_branch_policies":true}}' > /dev/null 2>&1; then
            print_success "Staging environment created"
        else
            print_warning "Staging environment may already exist or creation failed"
        fi
        
        # Create production environment
        if gh api repos/$REPO_OWNER/$REPO_NAME/environments/production -X PUT --input - <<< '{"wait_timer":300,"reviewers":[],"deployment_branch_policy":{"protected_branches":true,"custom_branch_policies":false}}' > /dev/null 2>&1; then
            print_success "Production environment created"
        else
            print_warning "Production environment may already exist or creation failed"
        fi
    fi
else
    print_info "Create environments manually at: https://github.com/$REPO_OWNER/$REPO_NAME/settings/environments"
    echo ""
    echo "Staging environment:"
    echo "- Name: staging"
    echo "- Deployment branches: develop, feature/*"
    echo "- Required reviewers: 0"
    echo "- Wait timer: 0 minutes"
    echo ""
    echo "Production environment:"
    echo "- Name: production" 
    echo "- Deployment branches: main only"
    echo "- Required reviewers: 1"
    echo "- Wait timer: 5 minutes"
fi

echo ""
echo "📋 Environment Variables"
echo "======================="

print_info "Set these variables for each environment:"
echo ""
echo "Staging variables:"
echo "RAG_MODEL_NAME=gpt-4o"
echo "TRANSCRIPTION_MODEL=gpt-4o-transcribe"
echo "ENABLE_TTS=true"
echo "LOG_LEVEL=INFO"
echo 'CORS_ORIGINS=["http://localhost:3000", "https://staging.yourapp.com"]'
echo ""
echo "Production variables:"
echo "RAG_MODEL_NAME=gpt-4o"
echo "TRANSCRIPTION_MODEL=gpt-4o-transcribe"
echo "ENABLE_TTS=true"
echo "LOG_LEVEL=WARNING"
echo 'CORS_ORIGINS=["https://yourapp.com", "https://www.yourapp.com"]'

echo ""
echo "🐳 Docker Registry"
echo "=================="

print_info "GitHub Container Registry will be used automatically"
print_info "Images will be published to: ghcr.io/$REPO_OWNER/$REPO_NAME"

# Check if package permissions are set correctly
if [ "$USE_GH_CLI" = true ]; then
    print_info "Checking repository permissions..."
    # This is a simplified check - actual permissions are more complex
    print_warning "Ensure 'Actions' have 'write' permissions for packages in repository settings"
fi

echo ""
echo "🎯 Next Steps"
echo "============="

echo "1. 📝 Review the generated workflows in .github/workflows/"
echo "2. 🔑 Set repository secrets (if not done automatically)"
echo "3. 🌍 Create environments and set variables"
echo "4. 🧪 Test by pushing to develop branch"
echo "5. 🚀 Create a release for production deployment"

echo ""
echo "📚 Documentation"
echo "================"
echo "- Full deployment guide: GITHUB_ACTIONS_DEPLOYMENT.md"
echo "- API documentation: API_README.md"
echo "- Configuration guide: VOICE_ASSISTANT_CONFIG.md"

echo ""
print_success "GitHub Actions setup completed!"
print_info "Check the workflows in .github/workflows/ and customize as needed"

echo ""
echo "🔗 Useful Links"
echo "==============="
if [ "$USE_GH_CLI" = true ]; then
    echo "Repository: $(gh repo view --web --json url -q .url)"
    echo "Actions: $(gh repo view --web --json url -q .url)/actions"
    echo "Settings: $(gh repo view --web --json url -q .url)/settings"
else
    echo "Repository: https://github.com/$REPO_OWNER/$REPO_NAME"
    echo "Actions: https://github.com/$REPO_OWNER/$REPO_NAME/actions"
    echo "Settings: https://github.com/$REPO_OWNER/$REPO_NAME/settings"
fi
