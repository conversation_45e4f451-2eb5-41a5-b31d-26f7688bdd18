#!/usr/bin/env python3
"""
Quick launcher for the Voice Streaming Widget

This script provides an easy way to test the widget before building the executable.
"""

import sys
import os
from pathlib import Path

def check_dependencies():
    """Check if PyQt6 is available"""
    try:
        import PyQt6
        return True
    except ImportError:
        print("ERROR: PyQt6 not installed")
        print("Install with: pip install PyQt6 PyQt6-WebEngine")
        return False

def main():
    """Launch the voice widget"""
    print("Voice Streaming Widget Launcher")
    print("=" * 40)

    # Check dependencies
    if not check_dependencies():
        sys.exit(1)

    # Check if widget file exists
    widget_file = Path("voice_widget.py")
    if not widget_file.exists():
        print("ERROR: voice_widget.py not found")
        sys.exit(1)

    print("Starting Voice Streaming Widget...")
    print("Press Ctrl+C to stop")
    print()

    # Import and run the widget
    try:
        from voice_widget import main as widget_main
        widget_main()
    except KeyboardInterrupt:
        print("\nWidget stopped by user")
    except Exception as e:
        print(f"ERROR: Error starting widget: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
