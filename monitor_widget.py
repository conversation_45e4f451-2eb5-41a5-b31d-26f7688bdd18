#!/usr/bin/env python3
"""
Monitor the widget for crashes and provide debugging information
"""

import time
import psutil
import subprocess

def get_widget_processes():
    """Get all widget processes"""
    processes = []
    for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'create_time', 'status']):
        if proc.info['name'] == 'VoiceStreamingWidget.exe':
            processes.append(proc.info)
    return processes

def monitor_widget():
    """Monitor widget for crashes"""
    print("Widget Crash Monitor")
    print("=" * 30)
    
    initial_processes = get_widget_processes()
    if not initial_processes:
        print("❌ No widget processes found. Please start the widget first.")
        return
    
    print(f"✅ Found {len(initial_processes)} widget process(es)")
    for proc in initial_processes:
        memory_mb = proc['memory_info'].rss / 1024 / 1024
        print(f"  - PID {proc['pid']}: {memory_mb:.1f} MB, status: {proc['status']}")
    
    print("\n🔍 Monitoring for crashes... (Press Ctrl+C to stop)")
    print("Instructions:")
    print("1. Click 'Start Server' in the widget")
    print("2. Click 'Start Streaming' button")
    print("3. Watch this monitor for any crashes")
    print()
    
    last_count = len(initial_processes)
    
    try:
        while True:
            time.sleep(2)  # Check every 2 seconds
            
            current_processes = get_widget_processes()
            current_count = len(current_processes)
            
            if current_count != last_count:
                if current_count < last_count:
                    print(f"⚠️  CRASH DETECTED! Process count dropped from {last_count} to {current_count}")
                    print("Widget may have crashed. Check the widget window.")
                elif current_count > last_count:
                    print(f"ℹ️  New process started. Count increased from {last_count} to {current_count}")
                
                last_count = current_count
                
                # Show current processes
                if current_processes:
                    print("Current processes:")
                    for proc in current_processes:
                        memory_mb = proc['memory_info'].rss / 1024 / 1024
                        uptime = time.time() - proc['create_time']
                        print(f"  - PID {proc['pid']}: {memory_mb:.1f} MB, uptime: {uptime:.1f}s")
                else:
                    print("❌ No widget processes running")
                print()
            
    except KeyboardInterrupt:
        print("\n👋 Monitoring stopped by user")
        
        # Final status
        final_processes = get_widget_processes()
        if final_processes:
            print(f"✅ Widget still running with {len(final_processes)} process(es)")
        else:
            print("❌ Widget is not running")

if __name__ == "__main__":
    monitor_widget()
