#!/usr/bin/env python3
"""
SSL Certificate Fix Script

This script fixes common SSL certificate issues in conda environments
by properly configuring certificate paths and environment variables.
"""

import os
import sys
import subprocess
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def check_certifi():
    """Check if certifi is installed and get certificate path"""
    try:
        import certifi
        cert_path = certifi.where()
        logger.info(f"✅ certifi found: {cert_path}")
        return cert_path
    except ImportError:
        logger.error("❌ certifi not found")
        return None

def install_certifi():
    """Install certifi package"""
    try:
        logger.info("📦 Installing certifi...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "certifi"])
        logger.info("✅ certifi installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install certifi: {e}")
        return False

def update_certificates():
    """Update conda certificates"""
    try:
        logger.info("🔄 Updating conda certificates...")
        subprocess.check_call(["conda", "update", "ca-certificates", "-y"])
        logger.info("✅ Conda certificates updated")
        return True
    except subprocess.CalledProcessError as e:
        logger.warning(f"⚠️ Could not update conda certificates: {e}")
        return False
    except FileNotFoundError:
        logger.warning("⚠️ conda command not found, skipping certificate update")
        return False

def set_environment_variables(cert_path):
    """Set SSL certificate environment variables"""
    env_vars = {
        'SSL_CERT_FILE': cert_path,
        'REQUESTS_CA_BUNDLE': cert_path,
        'CURL_CA_BUNDLE': cert_path
    }
    
    logger.info("🔧 Setting environment variables:")
    for var, path in env_vars.items():
        os.environ[var] = path
        logger.info(f"  {var} = {path}")
    
    return env_vars

def create_env_file(env_vars):
    """Create .env file with SSL certificate variables"""
    try:
        env_file_path = ".env"
        
        # Read existing .env file if it exists
        existing_vars = {}
        if os.path.exists(env_file_path):
            with open(env_file_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        existing_vars[key.strip()] = value.strip()
        
        # Update with SSL variables
        existing_vars.update(env_vars)
        
        # Write back to .env file
        with open(env_file_path, 'w') as f:
            f.write("# SSL Certificate Configuration\n")
            for var, path in env_vars.items():
                f.write(f"{var}={path}\n")
            f.write("\n")
            
            # Write other existing variables
            for key, value in existing_vars.items():
                if key not in env_vars:
                    f.write(f"{key}={value}\n")
        
        logger.info(f"✅ Environment variables saved to {env_file_path}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create .env file: {e}")
        return False

def test_ssl_connection():
    """Test SSL connection to verify fix"""
    try:
        import requests
        logger.info("🧪 Testing SSL connection...")
        
        response = requests.get("https://httpbin.org/get", timeout=10)
        if response.status_code == 200:
            logger.info("✅ SSL connection test successful")
            return True
        else:
            logger.warning(f"⚠️ SSL test returned status code: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ SSL connection test failed: {e}")
        return False

def main():
    """Main function to fix SSL certificates"""
    print("🔒 SSL Certificate Fix Script")
    print("=" * 40)
    
    # Step 1: Check if certifi is available
    cert_path = check_certifi()
    
    # Step 2: Install certifi if not available
    if not cert_path:
        if install_certifi():
            cert_path = check_certifi()
        else:
            logger.error("❌ Cannot proceed without certifi")
            return False
    
    # Step 3: Update conda certificates
    update_certificates()
    
    # Step 4: Set environment variables
    env_vars = set_environment_variables(cert_path)
    
    # Step 5: Create .env file
    create_env_file(env_vars)
    
    # Step 6: Test SSL connection
    test_ssl_connection()
    
    print("\n" + "=" * 40)
    print("🎉 SSL Certificate Fix Complete!")
    print("\n📋 Next Steps:")
    print("1. Restart your terminal/IDE")
    print("2. Run your voice streaming scripts")
    print("3. If issues persist, try:")
    print("   - Restart conda environment")
    print("   - Update conda: conda update conda")
    print("   - Reinstall packages: pip install --upgrade requests httpx")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Script interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        sys.exit(1)
