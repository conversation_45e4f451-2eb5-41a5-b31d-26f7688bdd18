#!/usr/bin/env python3
"""
Voice Streaming Desktop Widget

A standalone desktop widget for the voice streaming chatbot service.
Can be packaged as an executable for easy distribution.
"""

import sys
import os
import json
import threading
import time
import subprocess
import signal
import socket
from pathlib import Path

try:
    from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                                QHBoxLayout, QPushButton, QLabel, QSystemTrayIcon, 
                                QMenu, QCheckBox, QSpinBox, QGroupBox, QMessageBox,
                                QDialog, QDialogButtonBox, QFormLayout)
    from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QUrl, QSettings
    from PyQt6.QtGui import QIcon, QPixmap, QAction, QKeySequence, QShortcut
    from PyQt6.QtWebEngineWidgets import QWebEngineView
    from PyQt6.QtWebEngineCore import QWebEngineSettings
except ImportError:
    print("❌ PyQt6 not installed. Please install with: pip install PyQt6 PyQt6-WebEngine")
    sys.exit(1)

class VoiceServerThread(QThread):
    """Thread to run the voice server in background"""
    server_started = pyqtSignal()
    server_stopped = pyqtSignal()
    server_error = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.should_stop = False
        self.server_task = None

    def run(self):
        """Start the voice server"""
        try:
            print("Starting voice server thread...")

            # Apply tiktoken patches before importing voice server
            self.apply_tiktoken_patches()

            # Import the voice server function
            try:
                print("Importing voice server...")
                from websocket_voice_server import start_voice_server
                print("✓ Voice server imported successfully")
            except ImportError as e:
                print(f"✗ Could not import voice server: {e}")
                self.server_error.emit(f"Could not import voice server: {e}")
                return

            print("Emitting server_started signal...")
            self.server_started.emit()

            # Start the voice server directly in this thread
            import asyncio

            print("Creating asyncio event loop...")
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                print("Starting voice server...")
                # Start the server
                loop.run_until_complete(self.run_server())
            except Exception as e:
                print(f"✗ Voice server error: {e}")
                if not self.should_stop:
                    self.server_error.emit(f"Voice server error: {e}")
            finally:
                print("Closing asyncio event loop...")
                loop.close()

        except Exception as e:
            print(f"✗ Thread error: {e}")
            self.server_error.emit(str(e))
        finally:
            print("Voice server thread finished")
            self.server_stopped.emit()

    def apply_tiktoken_patches(self):
        """Apply tiktoken patches to handle encoding issues in PyInstaller"""
        try:
            import sys
            if getattr(sys, 'frozen', False):
                # We're running in a PyInstaller bundle
                import tiktoken

                # Store original functions if not already patched
                if not hasattr(tiktoken, '_original_get_encoding'):
                    tiktoken._original_get_encoding = tiktoken.get_encoding
                    tiktoken._original_encoding_for_model = tiktoken.encoding_for_model

                    def patched_get_encoding(encoding_name):
                        """Patched version that handles the character corruption issue"""
                        encoding_fixes = {
                            "c|100k_base": "cl100k_base",
                            "c1100k_base": "cl100k_base",
                            "cl|00k_base": "cl100k_base",
                            "cl1|0k_base": "cl100k_base",
                            "cl10|k_base": "cl100k_base",
                            "cl100|_base": "cl100k_base",
                            "cl100k|base": "cl100k_base"
                        }

                        if encoding_name in encoding_fixes:
                            encoding_name = encoding_fixes[encoding_name]

                        try:
                            return tiktoken._original_get_encoding(encoding_name)
                        except Exception as e:
                            print(f"Tiktoken encoding error for '{encoding_name}': {e}")
                            return tiktoken._original_get_encoding("cl100k_base")

                    def patched_encoding_for_model(model_name):
                        """Patched version for model encoding"""
                        try:
                            return tiktoken._original_encoding_for_model(model_name)
                        except Exception as e:
                            print(f"Tiktoken model encoding error for '{model_name}': {e}")
                            return patched_get_encoding("cl100k_base")

                    # Apply patches
                    tiktoken.get_encoding = patched_get_encoding
                    tiktoken.encoding_for_model = patched_encoding_for_model

                    print("Applied tiktoken patches for PyInstaller executable")

        except Exception as e:
            print(f"Error applying tiktoken patches: {e}")

    async def run_server(self):
        """Run the voice server with asyncio"""
        import asyncio
        import websockets
        from websocket_voice_server import handle_voice_stream

        print("Creating WebSocket server on localhost:8765...")
        async with websockets.serve(handle_voice_stream, "localhost", 8765):
            print("✓ WebSocket server started successfully")
            # Keep running until stop is requested
            while not self.should_stop:
                await asyncio.sleep(0.1)
        print("WebSocket server stopped")

    def stop_server(self):
        """Stop the voice server"""
        self.should_stop = True

class SettingsDialog(QDialog):
    """Settings dialog for widget configuration"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Voice Widget Settings")
        self.setModal(True)
        self.resize(400, 300)
        
        layout = QVBoxLayout()
        
        # Widget settings
        widget_group = QGroupBox("Widget Settings")
        widget_layout = QFormLayout()
        
        self.always_on_top = QCheckBox()
        self.always_on_top.setChecked(True)
        widget_layout.addRow("Always on top:", self.always_on_top)
        
        self.start_minimized = QCheckBox()
        widget_layout.addRow("Start minimized:", self.start_minimized)
        
        self.opacity_spin = QSpinBox()
        self.opacity_spin.setRange(20, 100)
        self.opacity_spin.setValue(95)
        self.opacity_spin.setSuffix("%")
        widget_layout.addRow("Opacity:", self.opacity_spin)
        
        widget_group.setLayout(widget_layout)
        layout.addWidget(widget_group)
        
        # Server settings
        server_group = QGroupBox("Server Settings")
        server_layout = QFormLayout()
        
        self.auto_start_server = QCheckBox()
        self.auto_start_server.setChecked(True)
        server_layout.addRow("Auto-start server:", self.auto_start_server)
        
        self.server_port = QSpinBox()
        self.server_port.setRange(1000, 65535)
        self.server_port.setValue(8765)
        server_layout.addRow("Server port:", self.server_port)
        
        server_group.setLayout(server_layout)
        layout.addWidget(server_group)
        
        # Dialog buttons
        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | 
                                 QDialogButtonBox.StandardButton.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        self.setLayout(layout)

class VoiceWidget(QMainWindow):
    """Main voice streaming widget window"""
    
    def __init__(self):
        super().__init__()
        self.settings = QSettings("VoiceWidget", "VoiceStreamingWidget")
        self.voice_server_thread = None
        self.server_running = False
        
        self.init_ui()
        self.init_system_tray()
        self.load_settings()
        self.setup_shortcuts()
        
        # Auto-start server if enabled
        if self.settings.value("auto_start_server", True, type=bool):
            self.start_voice_server()
    
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("🎙️ Voice Assistant Widget")
        self.setFixedSize(400, 600)
        
        # Set window flags for widget behavior
        self.setWindowFlags(Qt.WindowType.Window | 
                          Qt.WindowType.WindowStaysOnTopHint |
                          Qt.WindowType.WindowCloseButtonHint |
                          Qt.WindowType.WindowMinimizeButtonHint)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # Header
        header_layout = QHBoxLayout()
        
        self.status_label = QLabel("🔴 Server Stopped")
        self.status_label.setStyleSheet("font-weight: bold; padding: 5px;")
        header_layout.addWidget(self.status_label)
        
        header_layout.addStretch()
        
        # Control buttons
        self.server_btn = QPushButton("Start Server")
        self.server_btn.clicked.connect(self.toggle_voice_server)
        header_layout.addWidget(self.server_btn)
        
        settings_btn = QPushButton("⚙️")
        settings_btn.setFixedSize(30, 30)
        settings_btn.clicked.connect(self.show_settings)
        header_layout.addWidget(settings_btn)
        
        layout.addLayout(header_layout)
        
        # Web view for voice interface
        self.web_view = QWebEngineView()
        
        # Configure web engine settings
        settings = self.web_view.settings()
        settings.setAttribute(QWebEngineSettings.WebAttribute.JavascriptEnabled, True)
        settings.setAttribute(QWebEngineSettings.WebAttribute.LocalContentCanAccessRemoteUrls, True)
        settings.setAttribute(QWebEngineSettings.WebAttribute.AllowRunningInsecureContent, True)

        # Enable media features for audio/microphone access
        settings.setAttribute(QWebEngineSettings.WebAttribute.PlaybackRequiresUserGesture, False)
        settings.setAttribute(QWebEngineSettings.WebAttribute.AllowWindowActivationFromJavaScript, True)

        # Set up permission handling for microphone access
        self.web_view.page().featurePermissionRequested.connect(self.handle_permission_request)
        
        layout.addWidget(self.web_view)
        
        # Load the voice interface HTML
        self.load_voice_interface()

    def handle_permission_request(self, origin, permission):
        """Handle permission requests from the web page"""
        from PyQt6.QtWebEngineCore import QWebEnginePage

        # Grant microphone permission automatically
        if permission == QWebEnginePage.Feature.Microphone:
            print("Granting microphone permission")
            self.web_view.page().setFeaturePermission(origin, permission, QWebEnginePage.PermissionPolicy.PermissionGrantedByUser)
        elif permission == QWebEnginePage.Feature.MediaAudioCapture:
            print("Granting audio capture permission")
            self.web_view.page().setFeaturePermission(origin, permission, QWebEnginePage.PermissionPolicy.PermissionGrantedByUser)
        elif permission == QWebEnginePage.Feature.MediaVideoCapture:
            print("Granting video capture permission")
            self.web_view.page().setFeaturePermission(origin, permission, QWebEnginePage.PermissionPolicy.PermissionGrantedByUser)
        elif permission == QWebEnginePage.Feature.MediaAudioVideoCapture:
            print("Granting audio/video capture permission")
            self.web_view.page().setFeaturePermission(origin, permission, QWebEnginePage.PermissionPolicy.PermissionGrantedByUser)
        else:
            print(f"Unknown permission request: {permission}")
            # Grant by default for development
            self.web_view.page().setFeaturePermission(origin, permission, QWebEnginePage.PermissionPolicy.PermissionGrantedByUser)
    
    def load_voice_interface(self):
        """Load the voice streaming interface"""
        # Try multiple locations for the HTML file
        possible_paths = [
            Path("voice_streaming_component.html"),  # Current directory
            Path(__file__).parent / "voice_streaming_component.html",  # Same as script
            Path(sys.executable).parent / "voice_streaming_component.html",  # Same as executable
        ]

        html_file = None
        for path in possible_paths:
            if path.exists():
                html_file = path
                print(f"Found HTML file at: {html_file}")
                break

        if html_file:
            self.web_view.load(QUrl.fromLocalFile(str(html_file.absolute())))
        else:
            # Create a simple fallback interface
            fallback_html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Voice Widget</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 20px; }
                    .status { font-size: 18px; margin: 20px 0; }
                    .error { color: red; }
                </style>
            </head>
            <body>
                <h2>🎙️ Voice Assistant Widget</h2>
                <div class="status error">Voice interface not found</div>
                <p>Please ensure voice_streaming_component.html exists in the application directory.</p>
            </body>
            </html>
            """
            self.web_view.setHtml(fallback_html)
    
    def init_system_tray(self):
        """Initialize system tray icon"""
        if not QSystemTrayIcon.isSystemTrayAvailable():
            return
            
        # Create tray icon (using a simple text-based icon for now)
        self.tray_icon = QSystemTrayIcon(self)
        
        # Create tray menu
        tray_menu = QMenu()
        
        show_action = QAction("Show Widget", self)
        show_action.triggered.connect(self.show)
        tray_menu.addAction(show_action)
        
        hide_action = QAction("Hide Widget", self)
        hide_action.triggered.connect(self.hide)
        tray_menu.addAction(hide_action)
        
        tray_menu.addSeparator()
        
        settings_action = QAction("Settings", self)
        settings_action.triggered.connect(self.show_settings)
        tray_menu.addAction(settings_action)
        
        tray_menu.addSeparator()
        
        quit_action = QAction("Quit", self)
        quit_action.triggered.connect(self.quit_application)
        tray_menu.addAction(quit_action)
        
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.activated.connect(self.tray_icon_activated)
        
        # Show tray icon
        self.tray_icon.show()
    
    def setup_shortcuts(self):
        """Setup global shortcuts"""
        # Toggle widget visibility
        self.toggle_shortcut = QShortcut(QKeySequence("Ctrl+Shift+V"), self)
        self.toggle_shortcut.activated.connect(self.toggle_visibility)
    
    def toggle_visibility(self):
        """Toggle widget visibility"""
        if self.isVisible():
            self.hide()
        else:
            self.show()
            self.raise_()
            self.activateWindow()

    def tray_icon_activated(self, reason):
        """Handle tray icon activation"""
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            self.toggle_visibility()

    def start_voice_server(self):
        """Start the voice server in background"""
        if self.voice_server_thread and self.voice_server_thread.isRunning():
            return

        self.voice_server_thread = VoiceServerThread()
        self.voice_server_thread.server_started.connect(self.on_server_started)
        self.voice_server_thread.server_stopped.connect(self.on_server_stopped)
        self.voice_server_thread.server_error.connect(self.on_server_error)

        self.voice_server_thread.start()
        self.server_btn.setText("Starting...")
        self.server_btn.setEnabled(False)

    def stop_voice_server(self):
        """Stop the voice server"""
        if self.voice_server_thread and self.voice_server_thread.isRunning():
            self.voice_server_thread.stop_server()
            self.voice_server_thread.wait(5000)  # Wait up to 5 seconds

    def toggle_voice_server(self):
        """Toggle voice server on/off"""
        if self.server_running:
            self.stop_voice_server()
        else:
            self.start_voice_server()

    def on_server_started(self):
        """Handle server started signal"""
        print("✓ Voice server started successfully")
        self.server_running = True
        self.status_label.setText("🟢 Server Running")
        self.status_label.setStyleSheet("color: green; font-weight: bold; padding: 5px;")
        self.server_btn.setText("Stop Server")
        self.server_btn.setEnabled(True)

        # Reload web view to connect to server
        QTimer.singleShot(1000, self.load_voice_interface)

    def on_server_stopped(self):
        """Handle server stopped signal"""
        print("✓ Voice server stopped")
        self.server_running = False
        self.status_label.setText("🔴 Server Stopped")
        self.status_label.setStyleSheet("color: red; font-weight: bold; padding: 5px;")
        self.server_btn.setText("Start Server")
        self.server_btn.setEnabled(True)

    def on_server_error(self, error_msg):
        """Handle server error signal"""
        print(f"✗ Voice server error: {error_msg}")
        self.server_running = False
        self.status_label.setText("❌ Server Error")
        self.status_label.setStyleSheet("color: red; font-weight: bold; padding: 5px;")
        self.server_btn.setText("Start Server")
        self.server_btn.setEnabled(True)

        QMessageBox.warning(self, "Server Error", f"Voice server error:\n{error_msg}")

    def show_settings(self):
        """Show settings dialog"""
        dialog = SettingsDialog(self)

        # Load current settings
        dialog.always_on_top.setChecked(self.settings.value("always_on_top", True, type=bool))
        dialog.start_minimized.setChecked(self.settings.value("start_minimized", False, type=bool))
        dialog.opacity_spin.setValue(self.settings.value("opacity", 95, type=int))
        dialog.auto_start_server.setChecked(self.settings.value("auto_start_server", True, type=bool))
        dialog.server_port.setValue(self.settings.value("server_port", 8765, type=int))

        if dialog.exec() == QDialog.DialogCode.Accepted:
            # Save settings
            self.settings.setValue("always_on_top", dialog.always_on_top.isChecked())
            self.settings.setValue("start_minimized", dialog.start_minimized.isChecked())
            self.settings.setValue("opacity", dialog.opacity_spin.value())
            self.settings.setValue("auto_start_server", dialog.auto_start_server.isChecked())
            self.settings.setValue("server_port", dialog.server_port.value())

            # Apply settings
            self.apply_settings()

    def load_settings(self):
        """Load and apply saved settings"""
        self.apply_settings()

        # Start minimized if enabled
        if self.settings.value("start_minimized", False, type=bool):
            self.hide()

    def apply_settings(self):
        """Apply current settings to widget"""
        # Always on top
        always_on_top = self.settings.value("always_on_top", True, type=bool)
        flags = self.windowFlags()
        if always_on_top:
            flags |= Qt.WindowType.WindowStaysOnTopHint
        else:
            flags &= ~Qt.WindowType.WindowStaysOnTopHint
        self.setWindowFlags(flags)

        # Opacity
        opacity = self.settings.value("opacity", 95, type=int) / 100.0
        self.setWindowOpacity(opacity)

        # Show window if flags changed
        if self.isVisible():
            self.show()

    def closeEvent(self, event):
        """Handle close event - minimize to tray instead of closing"""
        if self.tray_icon and self.tray_icon.isVisible():
            self.hide()
            event.ignore()
        else:
            self.quit_application()

    def quit_application(self):
        """Quit the application completely"""
        # Stop voice server
        self.stop_voice_server()

        # Hide tray icon
        if self.tray_icon:
            self.tray_icon.hide()

        # Quit application
        QApplication.quit()

def check_single_instance():
    """Check if another instance is already running"""
    try:
        # Try to bind to a specific port to ensure single instance
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.bind(('localhost', 9999))  # Use a different port for instance checking
        return True, sock
    except socket.error:
        return False, None

def main():
    """Main function to start the voice widget"""
    # Check for single instance
    is_single, lock_socket = check_single_instance()
    if not is_single:
        print("ERROR: Another instance of Voice Widget is already running!")
        sys.exit(1)

    # Handle high DPI displays (PyQt6 compatible)
    try:
        QApplication.setHighDpiScaleFactorRoundingPolicy(
            Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)
    except AttributeError:
        pass  # Not available in all PyQt6 versions

    # These attributes are handled automatically in PyQt6

    app = QApplication(sys.argv)
    app.setQuitOnLastWindowClosed(False)  # Keep running when window is closed

    # Set application properties
    app.setApplicationName("Voice Streaming Widget")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("ERA-IGNITE")

    # Create and show widget
    widget = VoiceWidget()
    widget.show()

    # Handle Ctrl+C gracefully
    def cleanup_and_quit(sig, frame):
        widget.quit_application()
        if lock_socket:
            lock_socket.close()
        sys.exit(0)

    signal.signal(signal.SIGINT, cleanup_and_quit)

    # Start event loop
    try:
        sys.exit(app.exec())
    finally:
        if lock_socket:
            lock_socket.close()

if __name__ == "__main__":
    main()
