#!/usr/bin/env python3
"""
Voice Streaming Client

A standalone client script that connects to the voice streaming API server
running on your local machine. This client provides a web interface for
continuous voice chat without requiring the full server setup locally.
"""

# Fix SSL certificates BEFORE any other imports
import os
import sys

# Set SSL certificate environment variables immediately
try:
    import certifi
    cert_path = certifi.where()
    os.environ['SSL_CERT_FILE'] = cert_path
    os.environ['REQUESTS_CA_BUNDLE'] = cert_path
    os.environ['CURL_CA_BUNDLE'] = cert_path
except ImportError:
    pass

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# Now safe to import other modules
import asyncio
import websockets
import json
import base64
import tempfile
import wave
import numpy as np
from io import BytesIO
import logging
import threading
import time
import subprocess
import webbrowser
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VoiceStreamingClient:
    """Client for connecting to remote voice streaming server"""
    
    def __init__(self, server_host="localhost", server_port=8765, client_port=8504):
        self.server_host = server_host
        self.server_port = server_port
        self.client_port = client_port
        self.websocket = None
        self.connected = False
        
    async def connect_to_server(self):
        """Connect to the remote voice streaming server"""
        try:
            server_url = f"ws://{self.server_host}:{self.server_port}"
            logger.info(f"Connecting to voice server at {server_url}")
            
            self.websocket = await websockets.connect(server_url)
            self.connected = True
            logger.info("Successfully connected to voice server")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to voice server: {e}")
            self.connected = False
            return False
    
    async def send_audio_chunk(self, audio_data):
        """Send audio chunk to server"""
        if not self.connected or not self.websocket:
            return None
            
        try:
            message = {
                "type": "audio_chunk",
                "audio_data": audio_data
            }
            await self.websocket.send(json.dumps(message))
            
            # Wait for response
            response = await self.websocket.recv()
            return json.loads(response)
            
        except Exception as e:
            logger.error(f"Error sending audio chunk: {e}")
            return None
    
    async def start_recording(self):
        """Start recording session"""
        if not self.connected or not self.websocket:
            return None
            
        try:
            message = {"type": "start_recording"}
            await self.websocket.send(json.dumps(message))
            
            response = await self.websocket.recv()
            return json.loads(response)
            
        except Exception as e:
            logger.error(f"Error starting recording: {e}")
            return None
    
    async def stop_recording(self):
        """Stop recording session"""
        if not self.connected or not self.websocket:
            return None
            
        try:
            message = {"type": "stop_recording"}
            await self.websocket.send(json.dumps(message))
            
            response = await self.websocket.recv()
            return json.loads(response)
            
        except Exception as e:
            logger.error(f"Error stopping recording: {e}")
            return None
    
    async def ping_server(self):
        """Ping server for health check"""
        if not self.connected or not self.websocket:
            return False
            
        try:
            message = {"type": "ping"}
            await self.websocket.send(json.dumps(message))
            
            response = await self.websocket.recv()
            data = json.loads(response)
            return data.get("status") == "pong"
            
        except Exception as e:
            logger.error(f"Error pinging server: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from server"""
        if self.websocket:
            await self.websocket.close()
            self.connected = False
            logger.info("Disconnected from voice server")

def create_client_html(server_host="localhost", server_port=8765):
    """Create the client HTML interface"""
    return f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎙️ Voice Streaming Client</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }}
        
        .container {{
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }}
        
        .header {{
            text-align: center;
            margin-bottom: 30px;
        }}
        
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}
        
        .header p {{
            margin: 10px 0 0 0;
            opacity: 0.8;
            font-size: 1.1em;
        }}
        
        .controls {{
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
        }}
        
        .btn {{
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}
        
        .btn-primary {{
            background: #4CAF50;
            color: white;
        }}
        
        .btn-primary:hover {{
            background: #45a049;
            transform: translateY(-2px);
        }}
        
        .btn-danger {{
            background: #f44336;
            color: white;
        }}
        
        .btn-danger:hover {{
            background: #da190b;
            transform: translateY(-2px);
        }}
        
        .btn:disabled {{
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }}
        
        .status {{
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            font-weight: 600;
        }}
        
        .status.connected {{
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid #4CAF50;
        }}
        
        .status.disconnected {{
            background: rgba(244, 67, 54, 0.2);
            border: 2px solid #f44336;
        }}
        
        .status.recording {{
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #FFC107;
            animation: pulse 1s infinite;
        }}
        
        @keyframes pulse {{
            0% {{ opacity: 1; }}
            50% {{ opacity: 0.7; }}
            100% {{ opacity: 1; }}
        }}
        
        .audio-level {{
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            margin: 20px 0;
            overflow: hidden;
        }}
        
        .audio-level-bar {{
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #FFC107, #f44336);
            width: 0%;
            transition: width 0.1s ease;
            border-radius: 10px;
        }}
        
        .chat-container {{
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
        }}
        
        .message {{
            margin: 10px 0;
            padding: 10px 15px;
            border-radius: 15px;
            max-width: 80%;
        }}
        
        .message.user {{
            background: rgba(33, 150, 243, 0.3);
            margin-left: auto;
            text-align: right;
        }}
        
        .message.assistant {{
            background: rgba(76, 175, 80, 0.3);
            margin-right: auto;
        }}
        
        .server-info {{
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            font-size: 0.9em;
            opacity: 0.8;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎙️ Voice Streaming Client</h1>
            <p>Connect to your local voice assistant server</p>
        </div>
        
        <div class="server-info">
            <strong>Server:</strong> {server_host}:{server_port}
        </div>
        
        <div id="status" class="status disconnected">
            Disconnected from server
        </div>
        
        <div class="controls">
            <button id="connectBtn" class="btn btn-primary">Connect to Server</button>
            <button id="startBtn" class="btn btn-primary" disabled>Start Streaming</button>
            <button id="stopBtn" class="btn btn-danger" disabled>Stop Streaming</button>
        </div>
        
        <div class="audio-level">
            <div id="audioLevelBar" class="audio-level-bar"></div>
        </div>
        
        <div id="chatContainer" class="chat-container">
            <div class="message assistant">
                <strong>Assistant:</strong> Welcome! Click "Connect to Server" to begin, then "Start Streaming" to start voice chat.
            </div>
        </div>
    </div>

    <script>
        let websocket = null;
        let mediaRecorder = null;
        let audioContext = null;
        let isStreaming = false;
        let isConnected = false;
        let isAISpeaking = false;
        
        const connectBtn = document.getElementById('connectBtn');
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const status = document.getElementById('status');
        const audioLevelBar = document.getElementById('audioLevelBar');
        const chatContainer = document.getElementById('chatContainer');
        
        // Connect to server
        connectBtn.addEventListener('click', connectToServer);
        startBtn.addEventListener('click', startStreaming);
        stopBtn.addEventListener('click', stopStreaming);
        
        function connectToServer() {{
            const serverUrl = 'ws://{server_host}:{server_port}';

            websocket = new WebSocket(serverUrl);
            
            websocket.onopen = function(event) {{
                console.log('Connected to voice server');
                isConnected = true;
                updateStatus('connected', 'Connected to voice server');
                connectBtn.disabled = true;
                startBtn.disabled = false;
            }};
            
            websocket.onmessage = function(event) {{
                const data = JSON.parse(event.data);
                handleServerMessage(data);
            }};
            
            websocket.onclose = function(event) {{
                console.log('Disconnected from voice server');
                isConnected = false;
                updateStatus('disconnected', 'Disconnected from voice server');
                connectBtn.disabled = false;
                startBtn.disabled = true;
                stopBtn.disabled = true;
            }};
            
            websocket.onerror = function(error) {{
                console.error('WebSocket error:', error);
                updateStatus('disconnected', 'Connection error');
            }};
        }}
        
        function updateStatus(type, message) {{
            status.className = `status ${{type}}`;
            status.textContent = message;
        }}
        
        function addMessage(sender, text) {{
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${{sender}}`;
            messageDiv.innerHTML = `<strong>${{sender === 'user' ? 'You' : 'Assistant'}}:</strong> ${{text}}`;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }}
        
        function handleServerMessage(data) {{
            if (data.status === 'transcribed' && data.text) {{
                addMessage('user', data.text);
                
                if (data.ai_response) {{
                    addMessage('assistant', data.ai_response);
                    
                    // Play AI audio if available
                    if (data.ai_audio) {{
                        playAIAudio(data.ai_audio);
                    }}
                }}
            }} else if (data.status === 'recording') {{
                updateStatus('recording', 'Recording... Speak now');
                updateAudioLevel(data.rms || 0);
            }} else if (data.status === 'silence') {{
                updateAudioLevel(data.rms || 0);
            }}
        }}
        
        function updateAudioLevel(rms) {{
            const level = Math.min(rms * 100, 100);
            audioLevelBar.style.width = level + '%';
        }}
        
        function playAIAudio(base64Audio) {{
            try {{
                isAISpeaking = true;
                const audioData = atob(base64Audio);
                const arrayBuffer = new ArrayBuffer(audioData.length);
                const uint8Array = new Uint8Array(arrayBuffer);
                
                for (let i = 0; i < audioData.length; i++) {{
                    uint8Array[i] = audioData.charCodeAt(i);
                }}
                
                const blob = new Blob([arrayBuffer], {{ type: 'audio/mpeg' }});
                const audioUrl = URL.createObjectURL(blob);
                const audio = new Audio(audioUrl);
                
                audio.onended = function() {{
                    isAISpeaking = false;
                    URL.revokeObjectURL(audioUrl);
                }};
                
                audio.play();
            }} catch (error) {{
                console.error('Error playing AI audio:', error);
                isAISpeaking = false;
            }}
        }}
        
        async function startStreaming() {{
            try {{
                // Check if getUserMedia is available
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {{
                    throw new Error('getUserMedia is not supported in this browser');
                }}

                console.log('Requesting microphone access...');
                const stream = await navigator.mediaDevices.getUserMedia({{
                    audio: {{
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }}
                }});
                
                audioContext = new AudioContext({{ sampleRate: 16000 }});
                const source = audioContext.createMediaStreamSource(stream);
                const scriptProcessor = audioContext.createScriptProcessor(4096, 1, 1);
                
                source.connect(scriptProcessor);
                scriptProcessor.connect(audioContext.destination);
                
                // Send start recording message
                websocket.send(JSON.stringify({{ type: 'start_recording' }}));
                
                scriptProcessor.onaudioprocess = function(event) {{
                    if (isAISpeaking) return;
                    
                    const inputBuffer = event.inputBuffer;
                    const inputData = inputBuffer.getChannelData(0);
                    
                    const float32Array = new Float32Array(inputData);
                    const arrayBuffer = float32Array.buffer;
                    const uint8Array = new Uint8Array(arrayBuffer);
                    const base64Audio = btoa(String.fromCharCode.apply(null, uint8Array));
                    
                    if (websocket && websocket.readyState === WebSocket.OPEN) {{
                        websocket.send(JSON.stringify({{
                            type: 'audio_chunk',
                            audio_data: base64Audio
                        }}));
                    }}
                }};
                
                isStreaming = true;
                startBtn.disabled = true;
                stopBtn.disabled = false;
                updateStatus('recording', 'Streaming active - Speak naturally');
                
            }} catch (error) {{
                console.error('Error starting streaming:', error);

                let errorMessage = 'Error accessing microphone. ';
                if (error.name === 'NotAllowedError') {{
                    errorMessage += 'Permission denied. Please allow microphone access and refresh the page.';
                }} else if (error.name === 'NotFoundError') {{
                    errorMessage += 'No microphone found. Please check your audio devices.';
                }} else if (error.name === 'NotSupportedError') {{
                    errorMessage += 'Microphone not supported. Try using HTTPS or a different browser.';
                }} else {{
                    errorMessage += 'Please check permissions and try again. Error: ' + error.message;
                }}

                alert(errorMessage);
            }}
        }}
        
        function stopStreaming() {{
            if (audioContext) {{
                audioContext.close();
                audioContext = null;
            }}
            
            if (websocket && websocket.readyState === WebSocket.OPEN) {{
                websocket.send(JSON.stringify({{ type: 'stop_recording' }}));
            }}
            
            isStreaming = false;
            startBtn.disabled = false;
            stopBtn.disabled = true;
            updateStatus('connected', 'Connected to voice server');
            updateAudioLevel(0);
        }}
    </script>
</body>
</html>
"""

def start_client_server(html_content, port=8504):
    """Start a simple HTTP server to serve the client interface"""
    import http.server
    import socketserver
    from urllib.parse import urlparse
    
    class ClientHandler(http.server.SimpleHTTPRequestHandler):
        def do_GET(self):
            if self.path == '/' or self.path == '/index.html':
                self.send_response(200)
                self.send_header('Content-type', 'text/html')
                self.end_headers()
                self.wfile.write(html_content.encode('utf-8'))
            else:
                self.send_error(404)
    
    try:
        with socketserver.TCPServer(("", port), ClientHandler) as httpd:
            logger.info(f"Client server started at http://localhost:{port}")
            httpd.serve_forever()
    except Exception as e:
        logger.error(f"Error starting client server: {e}")

def main():
    """Main function to start the voice streaming client"""
    import argparse

    parser = argparse.ArgumentParser(description="Voice Streaming Client")
    parser.add_argument("--server-host", default="localhost", 
                       help="Voice server host (default: localhost)")
    parser.add_argument("--server-port", type=int, default=8765,
                       help="Voice server port (default: 8765)")
    parser.add_argument("--client-port", type=int, default=8504,
                       help="Client web interface port (default: 8504)")
    parser.add_argument("--no-browser", action="store_true",
                       help="Don't automatically open browser")
    
    args = parser.parse_args()
    
    print("🎙️ Starting Voice Streaming Client...")
    print("=" * 50)
    print(f"Server: {args.server_host}:{args.server_port}")
    print(f"Client: http://localhost:{args.client_port}")
    print("=" * 50)

    # Flush output to ensure it's visible
    sys.stdout.flush()
    
    # Create HTML content
    html_content = create_client_html(args.server_host, args.server_port)
    
    # Start client server in a separate thread
    print("🌐 Starting client web server...")
    sys.stdout.flush()
    server_thread = threading.Thread(
        target=start_client_server,
        args=(html_content, args.client_port),
        daemon=True
    )
    server_thread.start()

    # Wait a moment for server to start
    print("⏳ Waiting for client server to start...")
    sys.stdout.flush()
    time.sleep(2)
    
    # Open browser
    if not args.no_browser:
        try:
            webbrowser.open(f"http://localhost:{args.client_port}")
            print(f"🌐 Opened browser to http://localhost:{args.client_port}")
        except Exception as e:
            logger.warning(f"Could not open browser: {e}")
            print(f"📱 Please open http://localhost:{args.client_port} in your browser")
    else:
        print(f"📱 Open http://localhost:{args.client_port} in your browser")
    
    print("\n✨ Client is ready!")
    print("💡 Make sure your voice server is running on the specified host/port")
    print("🛑 Press Ctrl+C to stop the client")
    
    try:
        # Keep main thread alive
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Shutting down voice streaming client...")
        print("👋 Goodbye!")

if __name__ == "__main__":
    main()
