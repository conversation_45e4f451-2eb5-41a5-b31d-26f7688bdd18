name: Deploy with <PERSON><PERSON> Compose

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      image_tag:
        description: 'Docker image tag to deploy'
        required: false
        default: 'latest'
      use_simple_image:
        description: 'Use simple Docker image'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Create deployment directory
      run: |
        mkdir -p deployment
        cp docker-compose.yml deployment/
        cp nginx.conf deployment/ || echo "nginx.conf not found, skipping"
    
    - name: Configure environment variables
      run: |
        cat > deployment/.env << EOF
        # Environment: ${{ github.event.inputs.environment }}
        # Deployed at: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
        
        # Docker Configuration
        DOCKER_IMAGE=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}${{ github.event.inputs.use_simple_image == 'true' && '-simple' || '' }}:${{ github.event.inputs.image_tag }}
        
        # API Configuration
        API_KEY=${{ secrets.VOICE_API_KEY }}
        LOG_LEVEL=${{ github.event.inputs.environment == 'production' && 'WARNING' || 'INFO' }}
        
        # Application Configuration
        RAG_MODEL_NAME=${{ vars.RAG_MODEL_NAME || 'gpt-4o' }}
        TRANSCRIPTION_MODEL=${{ vars.TRANSCRIPTION_MODEL || 'gpt-4o-transcribe' }}
        ENABLE_TTS=${{ vars.ENABLE_TTS || 'true' }}
        USE_SIMPLE_MODE=${{ github.event.inputs.use_simple_image }}
        
        # CORS Configuration
        VOICE_API_CORS_ORIGINS=${{ vars.CORS_ORIGINS || '["*"]' }}
        EOF
    
    - name: Update docker-compose for deployment
      run: |
        cd deployment
        
        # Update docker-compose.yml to use the specific image
        sed -i 's|build: \.|image: ${DOCKER_IMAGE}|g' docker-compose.yml
        
        # Add environment file reference
        if ! grep -q "env_file:" docker-compose.yml; then
          sed -i '/environment:/i\    env_file:\n      - .env' docker-compose.yml
        fi
    
    - name: Deploy with Docker Compose
      run: |
        cd deployment
        
        echo "🚀 Deploying Voice Assistant API..."
        echo "Environment: ${{ github.event.inputs.environment }}"
        echo "Image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}${{ github.event.inputs.use_simple_image == 'true' && '-simple' || '' }}:${{ github.event.inputs.image_tag }}"
        
        # Pull the latest image
        docker-compose pull
        
        # Start the services
        if [ "${{ github.event.inputs.environment }}" = "production" ]; then
          docker-compose --profile production up -d
        else
          docker-compose up -d
        fi
        
        # Wait for services to be ready
        echo "⏳ Waiting for services to start..."
        sleep 30
        
        # Check health
        if curl -f http://localhost:8000/health; then
          echo "✅ Deployment successful!"
          echo "🌐 API available at: http://localhost:8000"
          echo "📚 Documentation at: http://localhost:8000/docs"
        else
          echo "❌ Health check failed"
          docker-compose logs
          exit 1
        fi
    
    - name: Run post-deployment tests
      run: |
        cd deployment
        
        echo "🧪 Running post-deployment tests..."
        
        # Test API endpoints
        curl -f http://localhost:8000/health || exit 1
        curl -f http://localhost:8000/docs || exit 1
        
        # Test with API key if provided
        if [ -n "${{ secrets.VOICE_API_KEY }}" ]; then
          curl -f -H "Authorization: Bearer ${{ secrets.VOICE_API_KEY }}" \
               http://localhost:8000/status || echo "⚠️ Status endpoint test failed"
        fi
        
        echo "✅ All tests passed!"
    
    - name: Cleanup on failure
      if: failure()
      run: |
        cd deployment
        echo "🧹 Cleaning up failed deployment..."
        docker-compose down
        docker-compose logs
    
    - name: Upload deployment artifacts
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: deployment-${{ github.event.inputs.environment }}-${{ github.run_number }}
        path: |
          deployment/
          !deployment/.env
        retention-days: 30
