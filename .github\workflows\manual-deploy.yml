name: Manual Deployment

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target environment'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      
      deployment_type:
        description: 'Deployment method'
        required: true
        default: 'docker-compose'
        type: choice
        options:
        - docker-compose
        - kubernetes
        - cloud-run
        - ec2
      
      image_tag:
        description: 'Docker image tag (leave empty for latest)'
        required: false
        default: ''
      
      use_simple_image:
        description: 'Use minimal Docker image'
        required: false
        default: false
        type: boolean
      
      skip_tests:
        description: 'Skip pre-deployment tests'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  pre-deployment-checks:
    if: ${{ !inputs.skip_tests }}
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Validate deployment inputs
      run: |
        echo "🔍 Validating deployment configuration..."
        echo "Environment: ${{ inputs.environment }}"
        echo "Deployment Type: ${{ inputs.deployment_type }}"
        echo "Image Tag: ${{ inputs.image_tag || 'latest' }}"
        echo "Use Simple Image: ${{ inputs.use_simple_image }}"
        
        # Validate environment
        if [[ "${{ inputs.environment }}" == "production" && "${{ github.ref }}" != "refs/heads/main" ]]; then
          echo "❌ Production deployments only allowed from main branch"
          exit 1
        fi
    
    - name: Test Docker image availability
      run: |
        IMAGE_TAG="${{ inputs.image_tag }}"
        if [ -z "$IMAGE_TAG" ]; then
          IMAGE_TAG="latest"
        fi
        
        IMAGE_SUFFIX=""
        if [ "${{ inputs.use_simple_image }}" = "true" ]; then
          IMAGE_SUFFIX="-simple"
        fi
        
        FULL_IMAGE="${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}${IMAGE_SUFFIX}:${IMAGE_TAG}"
        echo "🐳 Checking image: $FULL_IMAGE"
        
        # Try to pull the image to verify it exists
        docker pull "$FULL_IMAGE" || {
          echo "❌ Image not found: $FULL_IMAGE"
          echo "Available tags:"
          curl -s "https://api.github.com/repos/${{ github.repository }}/packages/container/${{ github.event.repository.name }}/versions" | jq -r '.[].metadata.container.tags[]' | head -10
          exit 1
        }

  deploy-docker-compose:
    if: inputs.deployment_type == 'docker-compose'
    needs: [pre-deployment-checks]
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup deployment environment
      run: |
        IMAGE_TAG="${{ inputs.image_tag }}"
        if [ -z "$IMAGE_TAG" ]; then
          IMAGE_TAG="latest"
        fi
        
        IMAGE_SUFFIX=""
        if [ "${{ inputs.use_simple_image }}" = "true" ]; then
          IMAGE_SUFFIX="-simple"
        fi
        
        FULL_IMAGE="${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}${IMAGE_SUFFIX}:${IMAGE_TAG}"
        
        echo "DEPLOYMENT_IMAGE=$FULL_IMAGE" >> $GITHUB_ENV
        echo "DEPLOYMENT_ENV=${{ inputs.environment }}" >> $GITHUB_ENV
    
    - name: Create deployment configuration
      run: |
        mkdir -p deployment
        cp docker-compose.yml deployment/
        
        # Create environment file
        cat > deployment/.env << EOF
        # Deployment Configuration
        DEPLOYMENT_TIME=$(date -u +"%Y-%m-%d %H:%M:%S UTC")
        DEPLOYMENT_ENV=${{ inputs.environment }}
        DEPLOYMENT_IMAGE=${DEPLOYMENT_IMAGE}
        
        # API Configuration
        VOICE_API_API_KEY=${{ secrets.VOICE_API_KEY }}
        LOG_LEVEL=${{ vars.LOG_LEVEL || 'INFO' }}
        
        # Application Settings
        RAG_MODEL_NAME=${{ vars.RAG_MODEL_NAME || 'gpt-4o' }}
        TRANSCRIPTION_MODEL=${{ vars.TRANSCRIPTION_MODEL || 'gpt-4o-transcribe' }}
        ENABLE_TTS=${{ vars.ENABLE_TTS || 'true' }}
        USE_SIMPLE_MODE=${{ inputs.use_simple_image }}
        
        # CORS Configuration
        VOICE_API_CORS_ORIGINS=${{ vars.CORS_ORIGINS || '["*"]' }}
        EOF
    
    - name: Deploy with Docker Compose
      run: |
        cd deployment
        
        # Update docker-compose to use specific image
        sed -i "s|build: \.|image: ${DEPLOYMENT_IMAGE}|g" docker-compose.yml
        
        echo "🚀 Starting deployment..."
        docker-compose pull
        docker-compose up -d
        
        # Wait for service to be ready
        echo "⏳ Waiting for service to start..."
        for i in {1..30}; do
          if curl -f http://localhost:8000/health; then
            echo "✅ Service is ready!"
            break
          fi
          echo "Attempt $i/30 - waiting..."
          sleep 10
        done
    
    - name: Run health checks
      run: |
        echo "🏥 Running health checks..."
        
        # Basic health check
        curl -f http://localhost:8000/health
        
        # API documentation check
        curl -f http://localhost:8000/docs
        
        # Status check with API key
        if [ -n "${{ secrets.VOICE_API_KEY }}" ]; then
          curl -f -H "Authorization: Bearer ${{ secrets.VOICE_API_KEY }}" \
               http://localhost:8000/status
        fi
        
        echo "✅ All health checks passed!"

  deploy-kubernetes:
    if: inputs.deployment_type == 'kubernetes'
    needs: [pre-deployment-checks]
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
    
    - name: Deploy to Kubernetes
      run: |
        echo "🚀 Kubernetes deployment not yet implemented"
        echo "Add your kubectl commands here"
        echo "Example:"
        echo "kubectl set image deployment/voice-assistant-api voice-assistant-api=${DEPLOYMENT_IMAGE}"
        echo "kubectl rollout status deployment/voice-assistant-api"

  deploy-cloud-run:
    if: inputs.deployment_type == 'cloud-run'
    needs: [pre-deployment-checks]
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Google Cloud CLI
      uses: google-github-actions/setup-gcloud@v1
      with:
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        project_id: ${{ secrets.GCP_PROJECT_ID }}
    
    - name: Deploy to Cloud Run
      run: |
        echo "🚀 Cloud Run deployment not yet implemented"
        echo "Add your gcloud run deploy commands here"

  deploy-ec2:
    if: inputs.deployment_type == 'ec2'
    needs: [pre-deployment-checks]
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to EC2
      run: |
        echo "🚀 EC2 deployment not yet implemented"
        echo "Add your EC2 deployment commands here"
        echo "Example: SSH to instance and run docker commands"

  post-deployment:
    needs: [deploy-docker-compose, deploy-kubernetes, deploy-cloud-run, deploy-ec2]
    if: always() && (needs.deploy-docker-compose.result == 'success' || needs.deploy-kubernetes.result == 'success' || needs.deploy-cloud-run.result == 'success' || needs.deploy-ec2.result == 'success')
    runs-on: ubuntu-latest
    
    steps:
    - name: Notify deployment success
      run: |
        echo "🎉 Deployment completed successfully!"
        echo "Environment: ${{ inputs.environment }}"
        echo "Method: ${{ inputs.deployment_type }}"
        echo "Image: ${{ env.DEPLOYMENT_IMAGE }}"
        
        # Add notification logic here (Slack, email, etc.)
