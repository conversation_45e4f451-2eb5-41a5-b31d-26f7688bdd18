# 🎙️ Voice Streaming Desktop Widget

A standalone desktop widget for the voice streaming chatbot service that can be compiled/packaged as a single executable file.

## 🚀 Quick Start

### Option 1: Run from Source (Development)
```bash
# Install dependencies
pip install PyQt6 PyQt6-WebEngine pyinstaller

# Run the widget directly
python run_widget.py
```

### Option 2: Build Executable (Distribution)
```bash
# Build the executable
python build_widget.py

# Run the executable (Windows)
./dist/VoiceStreamingWidget_Portable/VoiceStreamingWidget.exe

# Run the executable (Linux/Mac)
./dist/VoiceStreamingWidget_Portable/VoiceStreamingWidget
```

## ✨ Widget Features

### 🖥️ **Desktop Integration**
- **Standalone executable** - No installation required
- **System tray integration** - Minimize to system tray
- **Always-on-top option** - Keep widget visible over other windows
- **Compact floating window** - 400x600 pixel widget
- **Auto-start with system** - Optional startup integration

### 🎙️ **Voice Functionality**
- **Embedded voice interface** - Full voice streaming capabilities
- **Auto-start voice server** - Automatically manages backend server
- **Real-time audio streaming** - Continuous voice chat
- **Visual feedback** - Audio level indicators and status display

### ⚙️ **Customization**
- **Opacity control** - Adjust widget transparency (20-100%)
- **Hotkey support** - Ctrl+Shift+V to toggle visibility
- **Server port configuration** - Customize WebSocket port
- **Settings persistence** - Remembers your preferences

### 🔧 **Technical Features**
- **WebEngine integration** - Embeds existing HTML voice interface
- **Background server management** - Handles voice server lifecycle
- **Cross-platform** - Works on Windows, Linux, and macOS
- **Single-file executable** - Everything bundled in one file

## 📋 Widget Interface

```
┌─────────────────────────────────────┐
│ 🎙️ Voice Assistant Widget          │
├─────────────────────────────────────┤
│ 🟢 Server Running    [Stop] [⚙️]   │
├─────────────────────────────────────┤
│                                     │
│     Voice Streaming Interface       │
│                                     │
│  ┌─────────────────────────────┐    │
│  │ 🎙️ Start Streaming         │    │
│  │ ⏹️ Stop Streaming          │    │
│  │                             │    │
│  │ Chat Area:                  │    │
│  │ User: Hello                 │    │
│  │ AI: How can I help you?     │    │
│  │                             │    │
│  │ Audio Level: ████████░░     │    │
│  └─────────────────────────────┘    │
│                                     │
└─────────────────────────────────────┘
```

## 🎯 How It Works

### Architecture
1. **PyQt6 Desktop Widget** - Main application window
2. **Embedded WebEngine** - Displays voice streaming interface
3. **Background Voice Server** - Manages WebSocket voice processing
4. **System Integration** - Tray icon, shortcuts, settings

### Workflow
1. **Launch Widget** - Double-click executable or run from source
2. **Auto-Start Server** - Voice server starts automatically in background
3. **Voice Interface** - Embedded web view loads voice streaming component
4. **Continuous Chat** - Real-time voice conversation with AI
5. **System Integration** - Minimize to tray, use hotkeys, adjust settings

## ⚙️ Settings Panel

Access via the ⚙️ button or system tray menu:

### Widget Settings
- **Always on top**: Keep widget above other windows
- **Start minimized**: Launch widget minimized to tray
- **Opacity**: Adjust transparency (20-100%)

### Server Settings
- **Auto-start server**: Automatically start voice server on launch
- **Server port**: WebSocket server port (default: 8765)

## 🔧 Building the Executable

### Prerequisites
```bash
pip install PyQt6 PyQt6-WebEngine pyinstaller websockets langchain
```

### Build Process
```bash
# Run the automated build script
python build_widget.py
```

The build script will:
1. ✅ Check all dependencies
2. 🧹 Clean previous builds
3. 📁 Verify required files
4. 🔨 Build executable with PyInstaller
5. 📦 Create portable package

### Build Output
```
dist/
├── VoiceStreamingWidget.exe          # Single executable file
└── VoiceStreamingWidget_Portable/    # Portable package
    ├── VoiceStreamingWidget.exe      # Executable
    └── README.txt                    # Usage instructions
```

## 🎮 Usage Instructions

### First Launch
1. **Run executable** - Double-click the .exe file
2. **Grant permissions** - Allow microphone access if prompted
3. **Start server** - Click "Start Server" button
4. **Begin streaming** - Click "🎙️ Start Streaming" in the interface
5. **Speak naturally** - The AI will respond automatically

### Daily Usage
1. **Quick access** - Press Ctrl+Shift+V to show/hide widget
2. **System tray** - Right-click tray icon for options
3. **Always available** - Widget stays in system tray when closed

### Hotkeys
- **Ctrl+Shift+V** - Toggle widget visibility
- **Double-click tray icon** - Show/hide widget

## 🔍 Troubleshooting

### Common Issues

**Widget won't start**
- Ensure all dependencies are installed
- Check if PyQt6 and PyQt6-WebEngine are available
- Run `python run_widget.py` to test from source

**Server connection failed**
- Check if port 8765 is available
- Try changing server port in settings
- Ensure firewall allows the application

**No audio input**
- Grant microphone permissions to the application
- Check system audio settings
- Ensure microphone is not used by other applications

**Widget not visible**
- Press Ctrl+Shift+V to toggle visibility
- Check system tray for the widget icon
- Right-click tray icon and select "Show Widget"

### Debug Mode
Run from source for detailed error messages:
```bash
python voice_widget.py
```

## 📁 File Structure

```
voice_widget.py              # Main widget application
voice_widget.spec            # PyInstaller configuration
build_widget.py              # Automated build script
run_widget.py                # Development launcher
voice_streaming_component.html  # Voice interface HTML
websocket_voice_server.py    # Voice server backend
utils.py                     # Utility functions
config/                      # Configuration files
agent/                       # AI agent modules
src/                         # Source modules
```

## 🚀 Distribution

The built executable is completely self-contained and can be distributed as:

1. **Single file** - `VoiceStreamingWidget.exe` (Windows)
2. **Portable package** - Folder with executable and documentation
3. **Installer** - Can be wrapped with installer tools like NSIS or Inno Setup

## 🔮 Future Enhancements

- **Custom themes** - Dark/light mode support
- **Plugin system** - Extensible functionality
- **Auto-updater** - Automatic updates from repository
- **Multiple instances** - Support for multiple voice sessions
- **Cloud sync** - Sync settings across devices
