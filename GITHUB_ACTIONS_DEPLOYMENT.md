# GitHub Actions Deployment Guide

This guide explains how to deploy the Voice Assistant API using GitHub Actions CI/CD pipelines.

## 🚀 Quick Start

### Demo Mode (No API Key Required)

For team demos and testing, you can deploy without any API keys:

1. **Push to any branch** - GitHub Actions will build Docker images
2. **Use Demo Deployment workflow** - Go to Actions → "Demo Deployment" → Run workflow
3. **Download demo package** - Contains everything needed to run locally
4. **Run locally**: `docker-compose up -d`

### Production Setup

For production deployments with security:

#### Required Secrets:
Go to your GitHub repository → Settings → Secrets and variables → Actions
```
VOICE_API_KEY=your-secure-api-key-here
```

#### Optional Secrets:
```
OPENAI_API_KEY=sk-proj-your-openai-key
DATABASE_URL=********************************/db
SENTRY_DSN=https://<EMAIL>/project
```

### 2. Setup Environments

Go to Settings → Environments and create:

#### Staging Environment
- **Name**: `staging`
- **Deployment branches**: `develop`, `feature/*`
- **Required reviewers**: 0
- **Wait timer**: 0 minutes

#### Production Environment  
- **Name**: `production`
- **Deployment branches**: `main` only
- **Required reviewers**: 1
- **Wait timer**: 5 minutes

### 3. Configure Environment Variables

For each environment, add these variables:

#### Staging Variables:
```
RAG_MODEL_NAME=gpt-4o
TRANSCRIPTION_MODEL=gpt-4o-transcribe
ENABLE_TTS=true
LOG_LEVEL=INFO
CORS_ORIGINS=["http://localhost:3000", "https://staging.yourapp.com"]
```

#### Production Variables:
```
RAG_MODEL_NAME=gpt-4o
TRANSCRIPTION_MODEL=gpt-4o-transcribe
ENABLE_TTS=true
LOG_LEVEL=WARNING
CORS_ORIGINS=["https://yourapp.com", "https://www.yourapp.com"]
```

## 🔄 Deployment Workflows

### Demo Deployments (No API Key Required)

#### 1. **Demo Deployment** (`.github/workflows/demo-deploy.yml`)
- **Trigger**: Manual workflow dispatch
- **Perfect for**: Team demos, testing, development
- **Features**:
  - No API key required
  - Open CORS for easy testing
  - Configurable port and options
  - Complete demo package download
  - Ready-to-run Docker Compose setup

### Automatic Deployments

#### 2. **CI/CD Pipeline** (`.github/workflows/ci-cd.yml`)
- **Triggers**: Push to `main`/`develop`, Pull Requests, Releases
- **Actions**:
  - Run tests on multiple Python versions
  - Security scanning with Trivy
  - Build and push Docker images to GitHub Container Registry
  - Create demo deployment packages

#### 3. **Build and Test** (`.github/workflows/build-and-test.yml`)
- **Triggers**: Push to any branch, PRs, Daily schedule
- **Actions**:
  - Code linting (Black, flake8, isort)
  - Cross-platform testing (Ubuntu, Windows, macOS)
  - Docker build testing
  - Integration tests

### Production Deployments

#### 4. **Manual Deployment** (`.github/workflows/manual-deploy.yml`)
- **Trigger**: Manual workflow dispatch
- **Options**:
  - Choose environment (staging/production)
  - Select deployment method (docker-compose/kubernetes/cloud-run/ec2)
  - Specify image tag
  - Use simple or full Docker image
  - Skip tests option

#### 5. **Docker Compose Deployment** (`.github/workflows/deploy-docker-compose.yml`)
- **Trigger**: Manual workflow dispatch
- **Actions**: Deploy using Docker Compose with environment-specific configuration

## 🐳 Docker Images

The CI/CD pipeline builds two Docker images:

### Full Image (`ghcr.io/your-username/era-agents:tag`)
- Contains all dependencies including audio processing
- Suitable for full-featured deployments
- Larger size (~1GB)

### Simple Image (`ghcr.io/your-username/era-agents-simple:tag`)
- Minimal dependencies for API-only deployments
- Smaller size (~200MB)
- Perfect for cloud deployments

## 📋 Deployment Methods

### 1. Docker Compose (Recommended)

```bash
# Automatic via GitHub Actions
git push origin develop  # Deploys to staging
git tag v1.0.0 && git push origin v1.0.0  # Deploys to production

# Manual via GitHub Actions
# Go to Actions → Manual Deployment → Run workflow
```

### 2. Kubernetes

```yaml
# Add your Kubernetes manifests to k8s/ directory
# Update manual-deploy.yml with kubectl commands
```

### 3. Cloud Run (Google Cloud)

```bash
# Add GCP service account key to secrets
# Update manual-deploy.yml with gcloud commands
```

### 4. EC2 (AWS)

```bash
# Add AWS credentials to secrets
# Update manual-deploy.yml with deployment script
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `VOICE_API_KEY` | API authentication key | - | ✅ |
| `RAG_MODEL_NAME` | LLM model for RAG | `gpt-4o` | ❌ |
| `TRANSCRIPTION_MODEL` | Speech-to-text model | `gpt-4o-transcribe` | ❌ |
| `ENABLE_TTS` | Enable text-to-speech | `true` | ❌ |
| `LOG_LEVEL` | Logging level | `INFO` | ❌ |
| `CORS_ORIGINS` | Allowed CORS origins | `["*"]` | ❌ |

### Secrets Management

#### Repository Secrets (Global)
- Used across all environments
- Store sensitive data like API keys
- Access: `${{ secrets.SECRET_NAME }}`

#### Environment Secrets (Environment-specific)
- Override repository secrets for specific environments
- Higher precedence than repository secrets
- Access: `${{ secrets.SECRET_NAME }}`

#### Environment Variables (Non-sensitive)
- Store configuration that can be public
- Environment-specific settings
- Access: `${{ vars.VARIABLE_NAME }}`

## 🔍 Monitoring & Debugging

### Workflow Logs
- Go to Actions tab in your repository
- Click on any workflow run to see detailed logs
- Each job and step shows execution details

### Deployment Health Checks
All deployments include automatic health checks:
- API health endpoint: `GET /health`
- API documentation: `GET /docs`
- Authenticated status: `GET /status` (with API key)

### Common Issues

#### 1. **Image Not Found**
```bash
# Check available images
curl -s "https://api.github.com/repos/YOUR_USERNAME/era-agents/packages/container/era-agents/versions" | jq -r '.[].metadata.container.tags[]'
```

#### 2. **Permission Denied**
- Ensure `GITHUB_TOKEN` has package write permissions
- Check repository settings → Actions → General → Workflow permissions

#### 3. **Environment Not Found**
- Create environments in Settings → Environments
- Add required secrets and variables

#### 4. **Deployment Timeout**
- Increase health check timeout in workflow
- Check service logs for startup issues

## 🔒 Security Best Practices

### 1. API Keys
- Use strong, unique API keys for each environment
- Rotate keys regularly
- Never commit keys to code

### 2. Environment Isolation
- Use separate secrets for staging/production
- Restrict production deployments to main branch
- Require reviews for production deployments

### 3. Image Security
- Trivy security scanning on all builds
- Regular base image updates
- Non-root user in containers

### 4. Network Security
- Configure CORS appropriately
- Use HTTPS in production
- Implement rate limiting

## 📊 Monitoring

### GitHub Actions
- Workflow success/failure notifications
- Build time and resource usage
- Security scan results

### Application Monitoring
- Health check endpoints
- Structured logging
- Optional: Sentry integration for error tracking

## 🚀 Next Steps

1. **Setup Repository**: Add secrets and environment variables
2. **Test Deployment**: Push to develop branch to test staging deployment
3. **Production Release**: Create a release tag for production deployment
4. **Monitor**: Check workflow logs and application health
5. **Scale**: Add more deployment targets (Kubernetes, Cloud providers)

## 📞 Support

For issues with GitHub Actions deployment:
1. Check workflow logs in Actions tab
2. Verify secrets and environment configuration
3. Test Docker images locally
4. Review this documentation for troubleshooting steps

## 🎯 Example Deployment Flow

### Demo Deployment (Recommended for Teams)
```bash
# 1. Push any branch to build images
git push origin main  # or develop, or feature/demo

# 2. Go to GitHub Actions
# 3. Select "Demo Deployment" workflow
# 4. Click "Run workflow"
# 5. Choose branch and options
# 6. Download the demo package from artifacts
# 7. Extract and run: docker-compose up -d
```

### Quick Demo Setup
1. Go to GitHub repository
2. Click "Actions" tab
3. Select "Demo Deployment" workflow
4. Click "Run workflow"
5. Choose:
   - **Branch**: main or develop
   - **Simple image**: true (for faster download)
   - **Port**: 8000 (default)
6. Click "Run workflow" button
7. Wait for completion (~5 minutes)
8. Download the demo package
9. Extract and run: `docker-compose up -d`
10. Access: http://localhost:8000

### Production Deployment
```bash
# 1. Merge develop to main
git checkout main
git merge develop
git push origin main

# 2. Create release tag
git tag -a v1.0.0 -m "Release version 1.0.0"
git push origin v1.0.0

# 3. Use Manual Deployment workflow for production
# (requires proper secrets and environment setup)
```
