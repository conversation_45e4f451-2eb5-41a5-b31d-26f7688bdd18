# 🎯 Demo Deployment Guide

Perfect for team demos, testing, and development - **no API keys required!**

## 🚀 Quick Demo Setup (5 minutes)

### Step 1: Trigger Demo Build
1. Go to your GitHub repository
2. Click the **"Actions"** tab
3. Select **"Demo Deployment"** workflow
4. Click **"Run workflow"**
5. Configure options:
   - **Branch**: `main` or `develop`
   - **Simple image**: `true` (faster, smaller)
   - **Port**: `8000` (default)
   - **Enable Nginx**: `false` (for simplicity)
6. Click **"Run workflow"** button

### Step 2: Download Demo Package
1. Wait for workflow completion (~5 minutes)
2. Go to the completed workflow run
3. Scroll down to **"Artifacts"** section
4. Download: `voice-assistant-demo-[branch]-[number].zip`
5. Extract the ZIP file

### Step 3: Run Demo
```bash
# Navigate to extracted folder
cd voice-assistant-demo-main-123/

# Start the demo
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f
```

### Step 4: Access Demo
- **API**: http://localhost:8000
- **Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **WebSocket**: ws://localhost:8000/ws/voice

## 🎮 Demo Features

### ✅ What Works (No API Key Required)
- ✅ **API Server** - Full REST API functionality
- ✅ **WebSocket** - Real-time communication
- ✅ **Health Checks** - System status monitoring
- ✅ **API Documentation** - Interactive Swagger UI
- ✅ **CORS** - Open for all origins (demo-friendly)
- ✅ **Docker** - Containerized deployment
- ✅ **Logging** - Detailed application logs

### ⚠️ What's Limited (Demo Mode)
- ⚠️ **Authentication** - Uses demo key (no real security)
- ⚠️ **External APIs** - May need real API keys for full functionality
- ⚠️ **Persistence** - Data doesn't persist between restarts

## 🧪 Testing the Demo

### 1. Basic Health Check
```bash
curl http://localhost:8000/health
```
Expected response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "demo"
}
```

### 2. API Documentation
Open http://localhost:8000/docs in your browser to see:
- Interactive API documentation
- Try out endpoints directly
- View request/response schemas

### 3. WebSocket Test
```javascript
// Open browser console and run:
const ws = new WebSocket('ws://localhost:8000/ws/voice');
ws.onopen = () => console.log('✅ WebSocket connected');
ws.onmessage = (event) => console.log('📨 Received:', event.data);
ws.onerror = (error) => console.log('❌ Error:', error);

// Send a test message
ws.send(JSON.stringify({
  type: "test",
  message: "Hello from demo!"
}));
```

### 4. API Endpoints Test
```bash
# Test status endpoint
curl http://localhost:8000/status

# Test with demo API key
curl -H "Authorization: Bearer demo-no-auth-required" \
     http://localhost:8000/status
```

## 🛠 Demo Management

### View Logs
```bash
# All services
docker-compose logs

# Specific service
docker-compose logs voice-assistant-api

# Follow logs in real-time
docker-compose logs -f
```

### Restart Demo
```bash
# Restart all services
docker-compose restart

# Restart specific service
docker-compose restart voice-assistant-api
```

### Stop Demo
```bash
# Stop services (keeps containers)
docker-compose stop

# Stop and remove containers
docker-compose down

# Stop and remove everything (including volumes)
docker-compose down -v
```

### Resource Usage
```bash
# Check container status
docker-compose ps

# Monitor resource usage
docker stats

# Check disk usage
docker system df
```

## 🎯 Demo Scenarios

### Scenario 1: API Testing
1. Open http://localhost:8000/docs
2. Try the `/health` endpoint
3. Explore other available endpoints
4. Test with different parameters

### Scenario 2: WebSocket Communication
1. Open browser developer tools
2. Use the WebSocket test code above
3. Send different message types
4. Monitor real-time responses

### Scenario 3: Load Testing
```bash
# Simple load test with curl
for i in {1..10}; do
  curl http://localhost:8000/health &
done
wait
```

### Scenario 4: Integration Testing
1. Start the demo
2. Run your client application against http://localhost:8000
3. Test all integration points
4. Monitor logs for any issues

## 🔧 Customization

### Change Port
Edit `.env` file:
```bash
VOICE_API_API_PORT=8080
```
Then restart: `docker-compose restart`

### Enable Debug Logging
Edit `.env` file:
```bash
LOG_LEVEL=DEBUG
```
Then restart: `docker-compose restart`

### Use Different Image
Edit `docker-compose.yml`:
```yaml
services:
  voice-assistant-api:
    image: ghcr.io/your-username/era-agents-simple:main
```

## 🚨 Troubleshooting

### Port Already in Use
```bash
# Check what's using port 8000
lsof -i :8000

# Use different port
sed -i 's/8000:8000/8080:8000/g' docker-compose.yml
```

### Container Won't Start
```bash
# Check logs
docker-compose logs voice-assistant-api

# Check image exists
docker images | grep voice-assistant

# Pull latest image
docker-compose pull
```

### Can't Access API
```bash
# Check if container is running
docker-compose ps

# Check port mapping
docker port voice-assistant-api

# Test from inside container
docker-compose exec voice-assistant-api curl localhost:8000/health
```

### Memory Issues
```bash
# Check memory usage
docker stats

# Reduce memory limit in docker-compose.yml
# Or use simple image instead
```

## 📦 Demo Package Contents

When you download the demo package, you get:

```
voice-assistant-demo-main-123/
├── docker-compose.yml      # Configured for demo
├── .env                    # Demo environment variables
├── DEMO_INSTRUCTIONS.md    # This guide
└── nginx.conf             # (if nginx enabled)
```

## 🎉 Next Steps

After successful demo:

1. **Development**: Use this setup for local development
2. **Testing**: Integrate with your test suites
3. **Production**: Set up proper secrets and environments
4. **Scaling**: Consider Kubernetes or cloud deployment
5. **Monitoring**: Add proper logging and monitoring

## 📞 Demo Support

If you encounter issues with the demo:

1. **Check logs**: `docker-compose logs -f`
2. **Verify setup**: Ensure Docker and Docker Compose are installed
3. **Port conflicts**: Try different ports if 8000 is busy
4. **Resource limits**: Ensure sufficient memory/CPU
5. **Network issues**: Check firewall and network settings

---

**Happy demoing! 🎯**
