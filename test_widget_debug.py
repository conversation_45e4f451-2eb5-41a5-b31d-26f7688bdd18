#!/usr/bin/env python3
"""
Debug the widget server startup with detailed testing
"""

import time
import asyncio
import websockets
import json
import psutil
import socket

def check_widget_processes():
    """Check widget processes and their status"""
    widget_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'create_time']):
        if proc.info['name'] == 'VoiceStreamingWidget.exe':
            widget_processes.append(proc.info)
    
    if widget_processes:
        print(f"✓ Found {len(widget_processes)} widget process(es):")
        for proc in widget_processes:
            memory_mb = proc['memory_info'].rss / 1024 / 1024
            uptime = time.time() - proc['create_time']
            print(f"  - PID {proc['pid']}: {memory_mb:.1f} MB, uptime: {uptime:.1f}s")
        return True
    else:
        print("✗ No widget processes found")
        return False

def check_port_8765():
    """Check if port 8765 is in use"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', 8765))
        sock.close()
        
        if result == 0:
            print("✓ Port 8765 is open and accepting connections")
            return True
        else:
            print("✗ Port 8765 is not accepting connections")
            return False
    except Exception as e:
        print(f"✗ Error checking port 8765: {e}")
        return False

async def test_websocket_with_retries():
    """Test WebSocket connection with multiple retries"""
    max_retries = 5
    
    for attempt in range(1, max_retries + 1):
        try:
            print(f"WebSocket connection attempt {attempt}/{max_retries}...")
            
            uri = "ws://localhost:8765"
            async with websockets.connect(uri, timeout=5) as websocket:
                print("✓ WebSocket connected successfully!")
                
                # Send a test message
                test_msg = {"type": "test", "message": "Hello from debug test"}
                await websocket.send(json.dumps(test_msg))
                print("✓ Test message sent")
                
                # Try to receive response
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                    print(f"✓ Received response: {response}")
                except asyncio.TimeoutError:
                    print("⚠ No response received (this may be normal)")
                
                return True
                
        except Exception as e:
            print(f"✗ Attempt {attempt} failed: {e}")
            if attempt < max_retries:
                print(f"Waiting 2 seconds before retry...")
                await asyncio.sleep(2)
    
    print("✗ All WebSocket connection attempts failed")
    return False

def main():
    """Main debug function"""
    print("Voice Widget Debug Test")
    print("=" * 30)
    
    # Check if widget is running
    print("\n1. Checking widget processes...")
    if not check_widget_processes():
        print("\nWidget not running. Please start it first:")
        print(".\dist\VoiceStreamingWidget_Portable\VoiceStreamingWidget.exe")
        return
    
    # Wait for server to start
    print("\n2. Waiting for voice server to start...")
    for i in range(10):
        print(f"Waiting... {i+1}/10")
        time.sleep(1)
        
        # Check port every few seconds
        if i % 3 == 2:  # Check on 3rd, 6th, 9th second
            if check_port_8765():
                print("✓ Port became available!")
                break
    else:
        print("⚠ Port still not available after 10 seconds")
    
    # Final port check
    print("\n3. Final port check...")
    port_open = check_port_8765()
    
    # WebSocket test
    print("\n4. Testing WebSocket connection...")
    if port_open:
        success = asyncio.run(test_websocket_with_retries())
        
        if success:
            print("\n✅ Voice server is working correctly!")
            print("\nThe server is running. If 'Start Streaming' button is still")
            print("not working, the issue is likely in the HTML/JavaScript.")
        else:
            print("\n❌ WebSocket connection failed")
            print("The port is open but WebSocket handshake is failing.")
    else:
        print("\n❌ Port 8765 is not accessible")
        print("\nPossible issues:")
        print("1. Voice server thread is not starting")
        print("2. Server is starting but failing to bind to port")
        print("3. Firewall is blocking the connection")
        print("4. Another process is using port 8765")

if __name__ == "__main__":
    main()
