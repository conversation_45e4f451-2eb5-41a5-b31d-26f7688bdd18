#!/usr/bin/env python3
"""
Test starting the voice server manually to debug issues
"""

import asyncio
import sys
import traceback

def test_voice_server_import():
    """Test importing voice server components"""
    try:
        print("Testing voice server imports...")
        
        # Test basic imports
        import websockets
        print("✓ websockets imported")
        
        from websocket_voice_server import handle_voice_stream
        print("✓ handle_voice_stream imported")
        
        from websocket_voice_server import start_voice_server
        print("✓ start_voice_server imported")
        
        return True
        
    except Exception as e:
        print(f"✗ Import error: {e}")
        traceback.print_exc()
        return False

async def test_manual_server():
    """Test starting the voice server manually"""
    try:
        print("Starting manual voice server test...")
        
        import websockets
        from websocket_voice_server import handle_voice_stream
        
        print("Creating WebSocket server...")
        
        # Start server on localhost:8765
        async with websockets.serve(handle_voice_stream, "localhost", 8765):
            print("✓ Voice server started on localhost:8765")
            print("Server is running... (will run for 10 seconds)")
            
            # Keep running for 10 seconds
            await asyncio.sleep(10)
            
        print("✓ Server stopped cleanly")
        return True
        
    except Exception as e:
        print(f"✗ Server error: {e}")
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("Voice Server Manual Test")
    print("=" * 30)
    
    # Test imports first
    if not test_voice_server_import():
        print("\n❌ Import test failed")
        return
    
    print("\n✅ All imports successful")
    
    # Test manual server startup
    print("\nTesting manual server startup...")
    try:
        success = asyncio.run(test_manual_server())
        
        if success:
            print("\n✅ Manual server test successful!")
            print("\nThis means the voice server code is working.")
            print("The issue might be in the widget's server startup logic.")
        else:
            print("\n❌ Manual server test failed")
            
    except KeyboardInterrupt:
        print("\n⚠ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
