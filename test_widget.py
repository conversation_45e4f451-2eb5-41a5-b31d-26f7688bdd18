#!/usr/bin/env python3
"""
Test Script for Voice Streaming Widget

This script performs automated tests on the widget functionality.
"""

import time
import subprocess
import requests
import socket
import sys
import os
from pathlib import Path

def test_executable_exists():
    """Test if the executable exists"""
    exe_path = Path("dist/VoiceStreamingWidget_Portable/VoiceStreamingWidget.exe")
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"✅ Executable exists: {exe_path} ({size_mb:.1f} MB)")
        return True
    else:
        print(f"❌ Executable not found: {exe_path}")
        return False

def test_single_instance():
    """Test single instance functionality"""
    print("\n🔍 Testing single instance functionality...")
    
    # Start first instance
    print("Starting first instance...")
    proc1 = subprocess.Popen([
        "dist/VoiceStreamingWidget_Portable/VoiceStreamingWidget.exe"
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    time.sleep(3)  # Wait for first instance to start
    
    # Try to start second instance
    print("Attempting to start second instance...")
    proc2 = subprocess.Popen([
        "dist/VoiceStreamingWidget_Portable/VoiceStreamingWidget.exe"
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    time.sleep(2)  # Wait for second instance attempt
    
    # Check if both processes are still running
    proc1_running = proc1.poll() is None
    proc2_running = proc2.poll() is None
    
    print(f"First instance running: {proc1_running}")
    print(f"Second instance running: {proc2_running}")
    
    # Clean up
    if proc1_running:
        proc1.terminate()
        proc1.wait()
    if proc2_running:
        proc2.terminate()
        proc2.wait()
    
    if proc1_running and not proc2_running:
        print("✅ Single instance check working correctly")
        return True
    else:
        print("❌ Single instance check failed")
        return False

def test_voice_server_port():
    """Test if voice server port becomes available"""
    print("\n🔍 Testing voice server startup...")
    
    # Start widget
    print("Starting widget...")
    proc = subprocess.Popen([
        "dist/VoiceStreamingWidget_Portable/VoiceStreamingWidget.exe"
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    # Wait for startup
    time.sleep(5)
    
    # Check if voice server port is listening
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 8765))
        sock.close()
        
        if result == 0:
            print("✅ Voice server port 8765 is listening")
            server_running = True
        else:
            print("❌ Voice server port 8765 is not accessible")
            server_running = False
    except Exception as e:
        print(f"❌ Error checking voice server: {e}")
        server_running = False
    
    # Clean up
    if proc.poll() is None:
        proc.terminate()
        proc.wait()
    
    return server_running

def test_widget_files():
    """Test if required widget files exist"""
    print("\n🔍 Testing widget files...")
    
    required_files = [
        "voice_widget.py",
        "voice_widget.spec", 
        "build_widget.py",
        "run_widget.py",
        "voice_streaming_component.html",
        "websocket_voice_server.py",
    ]
    
    missing_files = []
    for file_name in required_files:
        if Path(file_name).exists():
            print(f"✅ {file_name}")
        else:
            print(f"❌ {file_name}")
            missing_files.append(file_name)
    
    if not missing_files:
        print("✅ All required files present")
        return True
    else:
        print(f"❌ Missing files: {missing_files}")
        return False

def test_portable_package():
    """Test portable package contents"""
    print("\n🔍 Testing portable package...")
    
    package_dir = Path("dist/VoiceStreamingWidget_Portable")
    if not package_dir.exists():
        print("❌ Portable package directory not found")
        return False
    
    required_files = [
        "VoiceStreamingWidget.exe",
        "README.txt",
    ]
    
    missing_files = []
    for file_name in required_files:
        file_path = package_dir / file_name
        if file_path.exists():
            print(f"✅ {file_name}")
        else:
            print(f"❌ {file_name}")
            missing_files.append(file_name)
    
    if not missing_files:
        print("✅ Portable package complete")
        return True
    else:
        print(f"❌ Missing package files: {missing_files}")
        return False

def main():
    """Run all tests"""
    print("🎙️ Voice Streaming Widget Test Suite")
    print("=" * 50)
    
    tests = [
        ("Executable Exists", test_executable_exists),
        ("Widget Files", test_widget_files),
        ("Portable Package", test_portable_package),
        ("Single Instance", test_single_instance),
        ("Voice Server", test_voice_server_port),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running test: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Widget is ready for use.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
