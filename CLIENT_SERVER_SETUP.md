# Voice Streaming Client-Server Setup

This guide explains how to set up the voice streaming system with a client-server architecture, allowing you to run the voice server on your local machine and connect clients from anywhere.

## 🏗️ Architecture Overview

```
┌─────────────────┐    WebSocket     ┌─────────────────┐
│                 │    Connection    │                 │
│  Client Machine │ ◄──────────────► │  Server Machine │
│                 │                  │                 │
│ - <PERSON> Browser   │                  │ - Voice Server  │
│ - Client Script │                  │ - AI Processing │
│ - Audio Capture │                  │ - TTS Generation│
└─────────────────┘                  └─────────────────┘
```

## 🚀 Quick Start

### Server Setup (Your Local Machine)

1. **Start the Voice Server:**
   ```bash
   python start_voice_server.py
   ```

2. **Or with custom settings:**
   ```bash
   python start_voice_server.py --host 0.0.0.0 --port 8765
   ```

### Client Setup (Any Machine)

1. **Start the Client:**
   ```bash
   python voice_streaming_client.py
   ```

2. **Or connect to remote server:**
   ```bash
   python voice_streaming_client.py --server-host YOUR_SERVER_IP --server-port 8765
   ```

3. **Open your browser to:** `http://localhost:8504`

## 📋 Detailed Setup Instructions

### 1. Server Machine Setup

The server machine runs the voice processing backend:

```bash
# Basic startup (localhost only)
python start_voice_server.py

# Allow external connections
python start_voice_server.py --host 0.0.0.0 --port 8765

# Enable debug logging
python start_voice_server.py --debug
```

**Server Options:**
- `--host`: Server host (default: localhost, use 0.0.0.0 for all interfaces)
- `--port`: Server port (default: 8765)
- `--debug`: Enable debug logging

### 2. Client Machine Setup

The client machine provides the web interface:

```bash
# Connect to local server
python voice_streaming_client.py

# Connect to remote server
python voice_streaming_client.py --server-host ************* --server-port 8765

# Use custom client port
python voice_streaming_client.py --client-port 8080

# Don't auto-open browser
python voice_streaming_client.py --no-browser
```

**Client Options:**
- `--server-host`: Voice server host (default: localhost)
- `--server-port`: Voice server port (default: 8765)
- `--client-port`: Client web interface port (default: 8504)
- `--no-browser`: Don't automatically open browser

## 🌐 Network Configuration

### For Local Network Access

1. **Server Machine:**
   ```bash
   python start_voice_server.py --host 0.0.0.0
   ```

2. **Find Server IP:**
   ```bash
   # Windows
   ipconfig
   
   # Linux/Mac
   ifconfig
   ```

3. **Client Machine:**
   ```bash
   python voice_streaming_client.py --server-host 192.168.1.XXX
   ```

### Firewall Configuration

Make sure the server port (default 8765) is open:

**Windows:**
```cmd
netsh advfirewall firewall add rule name="Voice Server" dir=in action=allow protocol=TCP localport=8765
```

**Linux (ufw):**
```bash
sudo ufw allow 8765
```

**Linux (iptables):**
```bash
sudo iptables -A INPUT -p tcp --dport 8765 -j ACCEPT
```

## 🎯 Usage Instructions

### 1. Start the Server
```bash
python start_voice_server.py --host 0.0.0.0
```

### 2. Start the Client
```bash
python voice_streaming_client.py --server-host YOUR_SERVER_IP
```

### 3. Use the Interface

1. **Connect:** Click "Connect to Server" in the web interface
2. **Start Streaming:** Click "Start Streaming" and allow microphone access
3. **Speak Naturally:** Talk normally, the system detects speech automatically
4. **View Responses:** See transcriptions and AI responses in real-time
5. **Stop:** Click "Stop Streaming" when done

## 🔧 Configuration

### Server Configuration

The server uses the same configuration files as the main application:
- `config/default.ini` - Default settings
- `config/local.ini` - Your custom overrides

### Client Configuration

The client is configured via command-line arguments. No additional configuration files needed.

## 🐛 Troubleshooting

### Connection Issues

1. **"Connection refused":**
   - Check if server is running
   - Verify host/port settings
   - Check firewall rules

2. **"WebSocket connection failed":**
   - Ensure server is accessible from client machine
   - Try connecting from server machine first (localhost)
   - Check network connectivity

3. **"Microphone access denied":**
   - Allow microphone permissions in browser
   - Use HTTPS for remote connections (browsers require it)
   - Check browser security settings

### Audio Issues

1. **No audio detected:**
   - Check microphone permissions
   - Verify audio levels in browser
   - Test with different browsers

2. **Poor transcription quality:**
   - Check microphone quality
   - Reduce background noise
   - Speak clearly and at normal volume

### Performance Issues

1. **Slow responses:**
   - Check network latency
   - Verify server resources (CPU/memory)
   - Consider running server closer to clients

2. **Audio dropouts:**
   - Check network stability
   - Reduce other network usage
   - Use wired connection if possible

## 📊 Monitoring

### Server Logs
```bash
# Start with debug logging
python start_voice_server.py --debug

# Monitor logs in real-time
tail -f voice_server.log
```

### Client Logs
- Open browser developer tools (F12)
- Check Console tab for JavaScript errors
- Monitor Network tab for WebSocket connections

## 🔒 Security Considerations

1. **Network Security:**
   - Use VPN for remote connections
   - Limit server access to trusted networks
   - Consider using reverse proxy with SSL

2. **Audio Privacy:**
   - Audio is processed in real-time
   - No permanent storage on client
   - Server may log transcriptions (check config)

3. **API Keys:**
   - Keep API keys secure on server
   - Don't expose keys to clients
   - Use environment variables

## 🚀 Advanced Usage

### Multiple Clients

The server supports multiple simultaneous client connections:

```bash
# Server (supports multiple clients)
python start_voice_server.py --host 0.0.0.0

# Client 1
python voice_streaming_client.py --client-port 8504

# Client 2  
python voice_streaming_client.py --client-port 8505

# Client 3
python voice_streaming_client.py --client-port 8506
```

### Load Balancing

For high-traffic scenarios, consider:
- Running multiple server instances on different ports
- Using a load balancer (nginx, HAProxy)
- Implementing session affinity

### SSL/HTTPS Setup

For production use with remote clients:

1. **Get SSL certificate**
2. **Configure reverse proxy (nginx):**
   ```nginx
   server {
       listen 443 ssl;
       server_name your-domain.com;
       
       ssl_certificate /path/to/cert.pem;
       ssl_certificate_key /path/to/key.pem;
       
       location / {
           proxy_pass http://localhost:8504;
       }
       
       location /ws {
           proxy_pass http://localhost:8765;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection "upgrade";
       }
   }
   ```

## 📞 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review server and client logs
3. Test with localhost first
4. Verify network connectivity
5. Check firewall and security settings

## 🎉 Success!

Once everything is working, you should see:
- ✅ Server running and accepting connections
- ✅ Client connected to server
- ✅ Audio streaming and transcription working
- ✅ AI responses generated and played back
- ✅ Continuous conversation flow

Enjoy your distributed voice streaming setup! 🎙️✨
