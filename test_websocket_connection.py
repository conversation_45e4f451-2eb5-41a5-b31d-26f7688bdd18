#!/usr/bin/env python3
"""
Test WebSocket connection to the voice server
"""

import asyncio
import websockets
import json

async def test_websocket_connection():
    """Test WebSocket connection to voice server"""
    try:
        print("Attempting to connect to voice server...")
        
        # Try to connect to the voice server
        uri = "ws://localhost:8765"
        
        async with websockets.connect(uri, timeout=10) as websocket:
            print("✓ Successfully connected to voice server!")
            
            # Send a test message
            test_message = {
                "type": "test",
                "message": "Hello from test client"
            }
            
            await websocket.send(json.dumps(test_message))
            print("✓ Sent test message to server")
            
            # Try to receive a response (with timeout)
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"✓ Received response: {response}")
            except asyncio.TimeoutError:
                print("⚠ No response received (this is normal for voice server)")
            
            return True
            
    except ConnectionRefusedError:
        print("✗ Connection refused - voice server may not be running")
        return False
    except asyncio.TimeoutError:
        print("✗ Connection timeout - voice server may not be responding")
        return False
    except Exception as e:
        print(f"✗ Connection error: {e}")
        return False

async def main():
    """Main test function"""
    print("Voice Server WebSocket Connection Test")
    print("=" * 40)
    
    success = await test_websocket_connection()
    
    if success:
        print("\n🎉 Voice server is working correctly!")
    else:
        print("\n❌ Voice server connection failed.")
        print("\nTroubleshooting:")
        print("1. Make sure the Voice Widget is running")
        print("2. Check if the server status shows 'Running' in the widget")
        print("3. Try restarting the widget")

if __name__ == "__main__":
    asyncio.run(main())
